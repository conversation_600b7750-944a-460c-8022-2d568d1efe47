<?php
$pageTitle = 'Student Management';
require_once __DIR__ . '/../includes/header.php';

// Handle search and pagination
$search = sanitizeInput($_GET['search'] ?? '');
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 25;
$offset = ($page - 1) * $limit;

try {
    $db = getDB();
    
    // Build search query
    $whereClause = "WHERE 1=1";
    $params = [];
    
    if (!empty($search)) {
        $whereClause .= " AND (s.name LIKE ? OR s.student_id LIKE ? OR s.email LIKE ? OR s.phone LIKE ?)";
        $searchTerm = "%$search%";
        $params = [$searchTerm, $searchTerm, $searchTerm, $searchTerm];
    }
    
    // Get total count
    $countStmt = $db->prepare("SELECT COUNT(*) as total FROM students s $whereClause");
    $countStmt->execute($params);
    $totalStudents = $countStmt->fetch()['total'];
    $totalPages = ceil($totalStudents / $limit);
    
    // Get students with pagination
    $stmt = $db->prepare("
        SELECT s.*, 
               (SELECT COUNT(*) FROM attendance a WHERE a.student_id = s.id AND DATE(a.date) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) AND a.status = 'present') as attendance_count,
               (SELECT SUM(amount) FROM payments p WHERE p.student_id = s.id AND MONTH(p.payment_date) = MONTH(CURDATE())) as monthly_payment
        FROM students s 
        $whereClause 
        ORDER BY s.created_at DESC 
        LIMIT $limit OFFSET $offset
    ");
    $stmt->execute($params);
    $students = $stmt->fetchAll();
    
    // Get statistics
    $statsStmt = $db->query("
        SELECT 
            COUNT(*) as total,
            SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active,
            SUM(CASE WHEN status = 'inactive' THEN 1 ELSE 0 END) as inactive
        FROM students
    ");
    $stats = $statsStmt->fetch();
    
} catch (Exception $e) {
    error_log("Students page error: " . $e->getMessage());
    $students = [];
    $totalStudents = $totalPages = 0;
    $stats = ['total' => 0, 'active' => 0, 'inactive' => 0];
}
?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="page-title mb-0">Student Management</h1>
        <p class="text-muted">Manage student registrations and information</p>
    </div>
    <div>
        <a href="<?php echo BASE_URL; ?>students/add.php" class="btn btn-add">
            <i class="fas fa-plus me-2"></i>Add Student
        </a>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="stats-card">
            <div class="d-flex align-items-center">
                <div class="stats-icon" style="background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);">
                    <i class="fas fa-users"></i>
                </div>
                <div class="ms-3">
                    <div class="text-muted small">Total Students</div>
                    <div class="h4 mb-0"><?php echo number_format($stats['total']); ?></div>
                    <div class="small" style="color: var(--primary-color);">Registered</div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="stats-card">
            <div class="d-flex align-items-center">
                <div class="stats-icon" style="background: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-light) 100%);">
                    <i class="fas fa-user-check"></i>
                </div>
                <div class="ms-3">
                    <div class="text-muted small">Active Students</div>
                    <div class="h4 mb-0"><?php echo number_format($stats['active']); ?></div>
                    <div class="small" style="color: var(--secondary-color);">Currently enrolled</div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="stats-card">
            <div class="d-flex align-items-center">
                <div class="stats-icon" style="background: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-light) 100%);">
                    <i class="fas fa-user-times"></i>
                </div>
                <div class="ms-3">
                    <div class="text-muted small">Inactive Students</div>
                    <div class="h4 mb-0"><?php echo number_format($stats['inactive']); ?></div>
                    <div class="small" style="color: var(--accent-color);">Needs attention</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Students Table -->
<div class="table-card">
    <div class="table-header">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h5 class="mb-0">Students List</h5>
            </div>
            <div class="col-md-6">
                <form method="GET" class="d-flex">
                    <input type="text" class="form-control me-2" name="search" 
                           placeholder="Search students..." value="<?php echo htmlspecialchars($search); ?>">
                    <button type="submit" class="btn btn-fresh">
                        <i class="fas fa-search"></i>
                    </button>
                    <?php if (!empty($search)): ?>
                        <a href="<?php echo BASE_URL; ?>students/index.php" class="btn btn-clear ms-2">
                            <i class="fas fa-times"></i>
                        </a>
                    <?php endif; ?>
                </form>
            </div>
        </div>
    </div>
    
    <div class="table-responsive">
        <table class="table table-hover mb-0">
            <thead class="table-light">
                <tr>
                    <th>Student</th>
                    <th>Contact</th>
                    <th>Status</th>
                    <th>Attendance (30d)</th>
                    <th>Monthly Payment</th>
                    <th>Registered</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($students)): ?>
                    <tr>
                        <td colspan="7" class="text-center text-muted py-4">
                            <?php echo empty($search) ? 'No students found' : 'No students match your search'; ?>
                        </td>
                    </tr>
                <?php else: ?>
                    <?php foreach ($students as $student): ?>
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar-circle me-3">
                                        <?php echo strtoupper(substr($student['name'], 0, 2)); ?>
                                    </div>
                                    <div>
                                        <div class="fw-medium"><?php echo htmlspecialchars($student['name']); ?></div>
                                        <div class="small text-muted"><?php echo htmlspecialchars($student['student_id']); ?></div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div><?php echo htmlspecialchars($student['email']); ?></div>
                                <div class="small text-muted"><?php echo htmlspecialchars($student['phone']); ?></div>
                            </td>
                            <td>
                                <span class="badge bg-<?php echo $student['status'] == 'active' ? 'success' : 'secondary'; ?>">
                                    <?php echo ucfirst($student['status']); ?>
                                </span>
                            </td>
                            <td>
                                <div class="fw-medium"><?php echo $student['attendance_count']; ?> days</div>
                                <div class="small text-muted">Last 30 days</div>
                            </td>
                            <td>
                                <?php if ($student['monthly_payment']): ?>
                                    <div class="fw-medium text-success">$<?php echo number_format($student['monthly_payment'], 2); ?></div>
                                <?php else: ?>
                                    <span class="text-danger">No payment</span>
                                <?php endif; ?>
                            </td>
                            <td class="text-muted">
                                <?php echo date('M j, Y', strtotime($student['created_at'])); ?>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="<?php echo BASE_URL; ?>students/view.php?id=<?php echo $student['id']; ?>"
                                       class="btn btn-outline-primary" data-bs-toggle="tooltip" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="<?php echo BASE_URL; ?>students/edit.php?id=<?php echo $student['id']; ?>"
                                       class="btn btn-outline-secondary" data-bs-toggle="tooltip" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="<?php echo BASE_URL; ?>students/delete.php?id=<?php echo $student['id']; ?>"
                                       class="btn btn-delete" data-bs-toggle="tooltip" title="Delete"
                                       onclick="return confirmDelete('Are you sure you want to delete this student?')">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
    
    <!-- Pagination -->
    <?php if ($totalPages > 1): ?>
        <div class="d-flex justify-content-between align-items-center p-3 border-top">
            <div class="text-muted">
                Showing <?php echo $offset + 1; ?> to <?php echo min($offset + $limit, $totalStudents); ?> 
                of <?php echo $totalStudents; ?> entries
            </div>
            <nav>
                <ul class="pagination pagination-sm mb-0">
                    <?php if ($page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo $page - 1; ?><?php echo $search ? '&search=' . urlencode($search) : ''; ?>">Previous</a>
                        </li>
                    <?php endif; ?>
                    
                    <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                        <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                            <a class="page-link" href="?page=<?php echo $i; ?><?php echo $search ? '&search=' . urlencode($search) : ''; ?>"><?php echo $i; ?></a>
                        </li>
                    <?php endfor; ?>
                    
                    <?php if ($page < $totalPages): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo $page + 1; ?><?php echo $search ? '&search=' . urlencode($search) : ''; ?>">Next</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </nav>
        </div>
    <?php endif; ?>
</div>

<style>
.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.875rem;
}
</style>

<?php require_once __DIR__ . '/../includes/footer.php'; ?>

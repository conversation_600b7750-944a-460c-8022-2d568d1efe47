<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Advanced Attendance System</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/shared.css" rel="stylesheet">
</head>
<body>
    <div class="layout-container">
        <!-- Navigation will be loaded here -->
        <div id="navigation-placeholder"></div>

        <!-- Main Content Area -->
        <main class="main-content" id="main-content">
            <!-- Mobile Header -->
            <div class="mobile-header">
                <div class="mobile-header-content">
                    <button class="mobile-toggle" onclick="toggleSidebar()">
                        <i class="fas fa-bars"></i>
                    </button>
                    <div class="logo">
                        <div class="logo-icon">
                            <i class="fas fa-fingerprint"></i>
                        </div>
                        Attendance System
                    </div>
                </div>
            </div>

            <!-- Page Header -->
            <div class="page-header">
                <h1 class="page-title">
                    <i class="fas fa-tachometer-alt"></i>
                    Dashboard
                </h1>
                <p class="page-subtitle">Overview of your attendance management system</p>
            </div>

            <!-- Quick Stats -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">Total Students</div>
                        <div class="stat-icon total">
                            <i class="fas fa-users"></i>
                        </div>
                    </div>
                    <div class="stat-value" id="total-students-stat">0</div>
                    <div class="stat-change">Registered students</div>
                </div>
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">Present Today</div>
                        <div class="stat-icon present">
                            <i class="fas fa-check"></i>
                        </div>
                    </div>
                    <div class="stat-value" id="present-today-stat">0</div>
                    <div class="stat-change">Students attended today</div>
                </div>
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">Payments Due</div>
                        <div class="stat-icon absent">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                    </div>
                    <div class="stat-value" id="payments-due-stat">0</div>
                    <div class="stat-change">Outstanding payments</div>
                </div>
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">Exam Eligible</div>
                        <div class="stat-icon present">
                            <i class="fas fa-graduation-cap"></i>
                        </div>
                    </div>
                    <div class="stat-value" id="exam-eligible-stat">0</div>
                    <div class="stat-change">Students eligible for exam</div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <div class="card-icon" style="background: var(--gradient-primary);">
                            <i class="fas fa-bolt"></i>
                        </div>
                        Quick Actions
                    </h3>
                </div>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                    <a href="registration.html" class="btn btn-primary">
                        <i class="fas fa-user-plus"></i>
                        Register Student
                    </a>
                    <a href="attendance.html" class="btn btn-success">
                        <i class="fas fa-calendar-check"></i>
                        View Attendance
                    </a>
                    <a href="finance.html" class="btn btn-warning" style="background: linear-gradient(135deg, var(--warning) 0%, #d97706 100%);">
                        <i class="fas fa-credit-card"></i>
                        Manage Payments
                    </a>
                    <a href="examination.html" class="btn btn-info" style="background: linear-gradient(135deg, var(--info) 0%, #0891b2 100%);">
                        <i class="fas fa-graduation-cap"></i>
                        Setup Exam
                    </a>
                </div>
            </div>

            <!-- System Status -->
            <div class="form-grid">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <div class="card-icon" style="background: var(--gradient-success);">
                                <i class="fas fa-server"></i>
                            </div>
                            System Status
                        </h3>
                    </div>
                    <div class="hardware-status">
                        <div class="hardware-card waiting" id="dashboard-rfid-card">
                            <div class="hardware-icon rfid">
                                <i class="fas fa-credit-card"></i>
                            </div>
                            <div class="hardware-title">RFID Scanner</div>
                            <div class="hardware-status-text waiting" id="dashboard-rfid-status">Checking...</div>
                            <div class="hardware-details">Hardware status</div>
                        </div>
                        
                        <div class="hardware-card waiting" id="dashboard-fingerprint-card">
                            <div class="hardware-icon fingerprint">
                                <i class="fas fa-fingerprint"></i>
                            </div>
                            <div class="hardware-title">Fingerprint Scanner</div>
                            <div class="hardware-status-text waiting" id="dashboard-fingerprint-status">Checking...</div>
                            <div class="hardware-details">Hardware status</div>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <div class="card-icon" style="background: var(--gradient-primary);">
                                <i class="fas fa-activity"></i>
                            </div>
                            Recent Activity
                        </h3>
                    </div>
                    <div class="activity-feed" id="dashboard-activity-feed">
                        <div class="activity-item">
                            <div class="activity-icon" style="background: var(--gradient-primary);">
                                <i class="fas fa-power-off"></i>
                            </div>
                            <div class="activity-content">
                                <div class="activity-text">System initialized</div>
                                <div class="activity-time">Just now</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Registrations -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <div class="card-icon" style="background: var(--gradient-success);">
                            <i class="fas fa-history"></i>
                        </div>
                        Recent Registrations
                    </h3>
                    <a href="registration.html" class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        Add New
                    </a>
                </div>
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th><i class="fas fa-user"></i> Name</th>
                                <th><i class="fas fa-envelope"></i> Email</th>
                                <th><i class="fas fa-book"></i> Course</th>
                                <th><i class="fas fa-check-circle"></i> Status</th>
                                <th><i class="fas fa-calendar"></i> Registered</th>
                            </tr>
                        </thead>
                        <tbody id="dashboard-registrations-data">
                            <!-- Populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </main>
    </div>

    <script src="assets/js/shared.js"></script>
    <script>
        // Dashboard specific functionality
        document.addEventListener('DOMContentLoaded', function() {
            loadNavigation();
            loadDashboardData();
            
            // Refresh data every 30 seconds
            setInterval(loadDashboardData, 30000);
        });

        // Load navigation component
        async function loadNavigation() {
            try {
                const response = await fetch('components/navigation.html');
                const html = await response.text();
                document.getElementById('navigation-placeholder').innerHTML = html;
            } catch (error) {
                console.error('Error loading navigation:', error);
            }
        }

        // Load dashboard data
        async function loadDashboardData() {
            try {
                await Promise.all([
                    loadStats(),
                    loadRecentRegistrations(),
                    updateHardwareStatus()
                ]);
            } catch (error) {
                console.error('Error loading dashboard data:', error);
            }
        }

        // Load statistics
        async function loadStats() {
            try {
                const response = await fetch('api/students.php?action=stats');
                const data = await response.json();
                
                if (data.success) {
                    document.getElementById('total-students-stat').textContent = data.total_students || 0;
                    document.getElementById('present-today-stat').textContent = data.present_today || 0;
                    document.getElementById('payments-due-stat').textContent = data.payments_due || 0;
                    document.getElementById('exam-eligible-stat').textContent = data.exam_eligible || 0;
                }
            } catch (error) {
                console.error('Error loading stats:', error);
            }
        }

        // Load recent registrations
        async function loadRecentRegistrations() {
            try {
                const response = await fetch('api/students.php?action=recent&limit=5');
                const data = await response.json();
                
                if (data.success && data.students) {
                    const columns = [
                        'name',
                        'email',
                        'course',
                        { key: 'status', render: (value) => AttendanceSystem.createBadge(value || 'Active', 'active') },
                        { key: 'created_at', render: (value) => AttendanceSystem.formatDate(value) }
                    ];
                    
                    AttendanceSystem.populateTable('dashboard-registrations-data', data.students, columns);
                }
            } catch (error) {
                console.error('Error loading recent registrations:', error);
            }
        }

        // Update hardware status
        async function updateHardwareStatus() {
            try {
                const response = await fetch('api/system.php?action=hardware_status');
                const data = await response.json();
                
                if (data.success) {
                    updateHardwareCard('dashboard-rfid-card', 'dashboard-rfid-status', data.rfid_status || 'offline');
                    updateHardwareCard('dashboard-fingerprint-card', 'dashboard-fingerprint-status', data.fingerprint_status || 'offline');
                }
            } catch (error) {
                console.error('Error updating hardware status:', error);
            }
        }

        function updateHardwareCard(cardId, statusId, status) {
            const card = document.getElementById(cardId);
            const statusText = document.getElementById(statusId);
            
            if (card && statusText) {
                card.className = `hardware-card ${status}`;
                statusText.className = `hardware-status-text ${status}`;
                statusText.textContent = status.charAt(0).toUpperCase() + status.slice(1);
            }
        }

        // Mobile sidebar toggle
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            if (sidebar) {
                sidebar.classList.toggle('mobile-visible');
            }
        }
    </script>
</body>
</html>

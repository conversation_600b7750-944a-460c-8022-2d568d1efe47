<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Registration - Advanced Attendance System</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/shared.css" rel="stylesheet">
</head>
<body>
    <div class="layout-container">
        <!-- Navigation will be loaded here -->
        <div id="navigation-placeholder"></div>

        <!-- Main Content Area -->
        <main class="main-content" id="main-content">
            <!-- Mobile Header -->
            <div class="mobile-header">
                <div class="mobile-header-content">
                    <button class="mobile-toggle" onclick="toggleSidebar()">
                        <i class="fas fa-bars"></i>
                    </button>
                    <div class="logo">
                        <div class="logo-icon">
                            <i class="fas fa-fingerprint"></i>
                        </div>
                        Attendance System
                    </div>
                </div>
            </div>

            <!-- Page Header -->
            <div class="page-header">
                <h1 class="page-title">
                    <i class="fas fa-user-plus"></i>
                    Student Registration
                </h1>
                <p class="page-subtitle">Register new students with advanced RFID and fingerprint authentication</p>
            </div>

            <!-- Hardware Status -->
            <div class="hardware-status">
                <div class="hardware-card waiting" id="rfid-card">
                    <div class="hardware-icon rfid" id="rfid-icon">
                        <i class="fas fa-credit-card"></i>
                    </div>
                    <div class="hardware-title">RFID Scanner</div>
                    <div class="hardware-status-text waiting" id="rfid-status-text">Waiting for card...</div>
                    <div class="hardware-details" id="rfid-details">Place RFID card near scanner</div>
                </div>
                
                <div class="hardware-card waiting" id="fingerprint-card">
                    <div class="hardware-icon fingerprint" id="fingerprint-icon">
                        <i class="fas fa-fingerprint"></i>
                    </div>
                    <div class="hardware-title">Fingerprint Scanner</div>
                    <div class="hardware-status-text waiting" id="fingerprint-status-text">Ready to capture</div>
                    <div class="hardware-details" id="fingerprint-details">Place finger on sensor</div>
                </div>
            </div>

            <!-- Registration Form -->
            <div class="form-grid">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <div class="card-icon" style="background: var(--gradient-primary);">
                                <i class="fas fa-user"></i>
                            </div>
                            Student Information
                        </h3>
                    </div>
                    <form id="registration-form">
                        <div class="form-group">
                            <label class="form-label" for="student-name">
                                <i class="fas fa-user"></i>
                                Full Name
                            </label>
                            <input type="text" id="student-name" class="form-input" placeholder="Enter student full name" required>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label" for="student-email">
                                <i class="fas fa-envelope"></i>
                                Email Address
                            </label>
                            <input type="email" id="student-email" class="form-input" placeholder="<EMAIL>" required>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label" for="student-course">
                                <i class="fas fa-book"></i>
                                Course
                            </label>
                            <select id="student-course" class="form-select" required>
                                <option value="">Select Course</option>
                                <option value="Computer Science">Computer Science</option>
                                <option value="Engineering">Engineering</option>
                                <option value="Business">Business</option>
                                <option value="Mathematics">Mathematics</option>
                                <option value="Physics">Physics</option>
                            </select>
                        </div>
                        
                        <button type="submit" id="register-btn" class="btn btn-primary" disabled>
                            <i class="fas fa-user-plus"></i>
                            Register Student
                        </button>
                    </form>
                </div>

                <!-- Real-time Activity Feed -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <div class="card-icon" style="background: var(--gradient-success);">
                                <i class="fas fa-activity"></i>
                            </div>
                            Live Activity
                        </h3>
                    </div>
                    <div class="activity-feed" id="activity-feed">
                        <div class="activity-item">
                            <div class="activity-icon" style="background: var(--gradient-primary);">
                                <i class="fas fa-power-off"></i>
                            </div>
                            <div class="activity-content">
                                <div class="activity-text">System initialized</div>
                                <div class="activity-time">Just now</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Registrations -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <div class="card-icon" style="background: var(--gradient-success);">
                            <i class="fas fa-history"></i>
                        </div>
                        Recent Registrations
                    </h3>
                    <button onclick="refreshRegistrations()" class="btn btn-primary">
                        <i class="fas fa-refresh"></i>
                        Refresh
                    </button>
                </div>
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th><i class="fas fa-user"></i> Name</th>
                                <th><i class="fas fa-envelope"></i> Email</th>
                                <th><i class="fas fa-book"></i> Course</th>
                                <th><i class="fas fa-credit-card"></i> RFID ID</th>
                                <th><i class="fas fa-check-circle"></i> Status</th>
                                <th><i class="fas fa-calendar"></i> Registered</th>
                            </tr>
                        </thead>
                        <tbody id="registrations-data">
                            <!-- Populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </main>
    </div>

    <script src="assets/js/shared.js"></script>
    <script>
        // Registration specific variables
        let currentRfidId = null;
        let currentFingerprintId = null;
        let isScanning = false;

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadNavigation();
            loadRegistrations();
            initializeRegistrationForm();
            startHardwareMonitoring();
        });

        // Load navigation component
        async function loadNavigation() {
            try {
                const response = await fetch('components/navigation.html');
                const html = await response.text();
                document.getElementById('navigation-placeholder').innerHTML = html;
            } catch (error) {
                console.error('Error loading navigation:', error);
            }
        }

        // Initialize registration form
        function initializeRegistrationForm() {
            const form = document.getElementById('registration-form');
            const registerBtn = document.getElementById('register-btn');
            
            form.addEventListener('submit', handleRegistration);
            
            // Enable/disable register button based on form completion
            const inputs = form.querySelectorAll('input, select');
            inputs.forEach(input => {
                input.addEventListener('input', checkFormCompletion);
            });
        }

        // Check if form is complete
        function checkFormCompletion() {
            const name = document.getElementById('student-name').value.trim();
            const email = document.getElementById('student-email').value.trim();
            const course = document.getElementById('student-course').value;
            const registerBtn = document.getElementById('register-btn');
            
            const isComplete = name && email && course && currentRfidId && currentFingerprintId;
            registerBtn.disabled = !isComplete;
        }

        // Handle registration form submission
        async function handleRegistration(e) {
            e.preventDefault();
            
            const name = document.getElementById('student-name').value.trim();
            const email = document.getElementById('student-email').value.trim();
            const course = document.getElementById('student-course').value;
            
            if (!AttendanceSystem.validateEmail(email)) {
                AttendanceSystem.showNotification('Please enter a valid email address', 'error');
                return;
            }
            
            if (!currentRfidId || !currentFingerprintId) {
                AttendanceSystem.showNotification('Please scan RFID card and fingerprint first', 'warning');
                return;
            }
            
            AttendanceSystem.setButtonLoading('register-btn', true);
            
            try {
                const response = await AttendanceSystem.apiRequest('api/students.php', {
                    method: 'POST',
                    body: JSON.stringify({
                        action: 'register',
                        name: name,
                        email: email,
                        course: course,
                        rfid_id: currentRfidId,
                        fingerprint_id: currentFingerprintId
                    })
                });
                
                if (response.success) {
                    AttendanceSystem.showNotification('Student registered successfully!', 'success');
                    AttendanceSystem.addActivityItem('activity-feed', 'user-plus', `Registered ${name}`, 'var(--gradient-success)');
                    
                    // Reset form
                    document.getElementById('registration-form').reset();
                    currentRfidId = null;
                    currentFingerprintId = null;
                    updateHardwareStatus();
                    checkFormCompletion();
                    
                    // Refresh registrations table
                    loadRegistrations();
                } else {
                    AttendanceSystem.showNotification(response.message || 'Registration failed', 'error');
                }
            } catch (error) {
                console.error('Registration error:', error);
                AttendanceSystem.showNotification('Registration failed. Please try again.', 'error');
            } finally {
                AttendanceSystem.setButtonLoading('register-btn', false);
            }
        }

        // Load registrations data
        async function loadRegistrations() {
            try {
                const response = await AttendanceSystem.apiRequest('api/students.php?action=list');
                
                if (response.success && response.students) {
                    const columns = [
                        'name',
                        'email',
                        'course',
                        'rfid_id',
                        { key: 'status', render: (value) => AttendanceSystem.createBadge(value || 'Active', 'active') },
                        { key: 'created_at', render: (value) => AttendanceSystem.formatDateTime(value) }
                    ];
                    
                    AttendanceSystem.populateTable('registrations-data', response.students, columns);
                }
            } catch (error) {
                console.error('Error loading registrations:', error);
            }
        }

        // Refresh registrations
        function refreshRegistrations() {
            loadRegistrations();
            AttendanceSystem.showNotification('Registrations refreshed', 'info');
        }

        // Start hardware monitoring
        function startHardwareMonitoring() {
            updateHardwareStatus();
            
            // Check for RFID and fingerprint data every 2 seconds
            setInterval(checkHardwareData, 2000);
        }

        // Check hardware data
        async function checkHardwareData() {
            try {
                const response = await fetch('api/rfid.php?action=check');
                const data = await response.json();
                
                if (data.success && data.rfid_id && !currentRfidId) {
                    currentRfidId = data.rfid_id;
                    updateRfidStatus('ready', `Card detected: ${currentRfidId}`);
                    AttendanceSystem.addActivityItem('activity-feed', 'credit-card', `RFID card scanned: ${currentRfidId}`, 'var(--gradient-primary)');
                    checkFormCompletion();
                }
                
                // Check fingerprint
                const fpResponse = await fetch('api/fingerprint.php?action=check');
                const fpData = await fpResponse.json();
                
                if (fpData.success && fpData.fingerprint_id && !currentFingerprintId) {
                    currentFingerprintId = fpData.fingerprint_id;
                    updateFingerprintStatus('ready', `Fingerprint captured`);
                    AttendanceSystem.addActivityItem('activity-feed', 'fingerprint', 'Fingerprint captured successfully', 'var(--gradient-success)');
                    checkFormCompletion();
                }
            } catch (error) {
                console.error('Error checking hardware data:', error);
            }
        }

        // Update hardware status
        function updateHardwareStatus() {
            if (!currentRfidId) {
                updateRfidStatus('waiting', 'Waiting for card...');
            }
            
            if (!currentFingerprintId) {
                updateFingerprintStatus('waiting', 'Ready to capture');
            }
        }

        // Update RFID status
        function updateRfidStatus(status, message) {
            const card = document.getElementById('rfid-card');
            const statusText = document.getElementById('rfid-status-text');
            const icon = document.getElementById('rfid-icon');
            
            card.className = `hardware-card ${status}`;
            statusText.className = `hardware-status-text ${status}`;
            statusText.textContent = message;
            
            if (status === 'ready') {
                icon.classList.remove('scanning');
            } else if (status === 'waiting') {
                icon.classList.add('scanning');
            }
        }

        // Update fingerprint status
        function updateFingerprintStatus(status, message) {
            const card = document.getElementById('fingerprint-card');
            const statusText = document.getElementById('fingerprint-status-text');
            const icon = document.getElementById('fingerprint-icon');
            
            card.className = `hardware-card ${status}`;
            statusText.className = `hardware-status-text ${status}`;
            statusText.textContent = message;
            
            if (status === 'ready') {
                icon.classList.remove('scanning');
            } else if (status === 'waiting') {
                icon.classList.add('scanning');
            }
        }

        // Mobile sidebar toggle
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            if (sidebar) {
                sidebar.classList.toggle('mobile-visible');
            }
        }
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Examination Management - Advanced Attendance System</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/shared.css" rel="stylesheet">
</head>
<body>
    <div class="layout-container">
        <!-- Navigation will be loaded here -->
        <div id="navigation-placeholder"></div>

        <!-- Main Content Area -->
        <main class="main-content" id="main-content">
            <!-- Mobile Header -->
            <div class="mobile-header">
                <div class="mobile-header-content">
                    <button class="mobile-toggle" onclick="toggleSidebar()">
                        <i class="fas fa-bars"></i>
                    </button>
                    <div class="logo">
                        <div class="logo-icon">
                            <i class="fas fa-fingerprint"></i>
                        </div>
                        Attendance System
                    </div>
                </div>
            </div>

            <!-- Page Header -->
            <div class="page-header">
                <h1 class="page-title">
                    <i class="fas fa-graduation-cap"></i>
                    Examination Management
                </h1>
                <p class="page-subtitle">Control exam access based on attendance and payment eligibility</p>
            </div>

            <!-- Exam Statistics -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">Eligible</div>
                        <div class="stat-icon present">
                            <i class="fas fa-check"></i>
                        </div>
                    </div>
                    <div class="stat-value" id="eligible-count">0</div>
                    <div class="stat-change">Students eligible</div>
                </div>
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">Ineligible</div>
                        <div class="stat-icon absent">
                            <i class="fas fa-times"></i>
                        </div>
                    </div>
                    <div class="stat-value" id="ineligible-count">0</div>
                    <div class="stat-change">Students ineligible</div>
                </div>
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">Checked In</div>
                        <div class="stat-icon present">
                            <i class="fas fa-user-check"></i>
                        </div>
                    </div>
                    <div class="stat-value" id="checked-in-count">0</div>
                    <div class="stat-change">Students in exam</div>
                </div>
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">Exam Status</div>
                        <div class="stat-icon total">
                            <i class="fas fa-clock"></i>
                        </div>
                    </div>
                    <div class="stat-value" id="exam-status">Not Started</div>
                    <div class="stat-change" id="exam-duration">No active exam</div>
                </div>
            </div>

            <!-- Exam Configuration -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <div class="card-icon" style="background: var(--gradient-primary);">
                            <i class="fas fa-cog"></i>
                        </div>
                        Exam Configuration
                    </h3>
                </div>
                <div class="form-grid" style="grid-template-columns: 1fr;">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1rem; margin-bottom: 1rem;">
                        <div class="form-group">
                            <label class="form-label" for="course-name">
                                <i class="fas fa-book"></i>
                                Course Name
                            </label>
                            <input type="text" id="course-name" class="form-input" placeholder="Enter course name for exam">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label" for="exam-date">
                                <i class="fas fa-calendar"></i>
                                Exam Date
                            </label>
                            <input type="date" id="exam-date" class="form-input">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label" for="exam-time">
                                <i class="fas fa-clock"></i>
                                Exam Time
                            </label>
                            <input type="time" id="exam-time" class="form-input">
                        </div>
                    </div>
                    
                    <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
                        <button id="setup-exam-btn" onclick="setupExam()" class="btn btn-primary">
                            <i class="fas fa-cog"></i>
                            Setup Exam
                        </button>
                        <button id="start-exam-btn" onclick="startExamSession()" class="btn btn-success" disabled>
                            <i class="fas fa-play"></i>
                            Start Session
                        </button>
                        <button id="end-exam-btn" onclick="endExamSession()" class="btn btn-danger" disabled>
                            <i class="fas fa-stop"></i>
                            End Session
                        </button>
                        <button onclick="refreshExamData()" class="btn btn-info" style="background: linear-gradient(135deg, var(--info) 0%, #0891b2 100%);">
                            <i class="fas fa-refresh"></i>
                            Refresh
                        </button>
                    </div>
                </div>
            </div>

            <!-- Email Actions -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <div class="card-icon" style="background: var(--gradient-warning);">
                            <i class="fas fa-envelope"></i>
                        </div>
                        Email Notifications
                    </h3>
                </div>
                <div style="display: flex; gap: 0.5rem; flex-wrap: wrap; margin-bottom: 20px;">
                    <button onclick="sendTestWarningEmail()" class="btn btn-info" style="background: linear-gradient(135deg, var(--info) 0%, #0891b2 100%);">
                        <i class="fas fa-flask"></i>
                        Send Test Email
                    </button>
                    
                    <button onclick="previewWarningEmails()" class="btn btn-primary">
                        <i class="fas fa-eye"></i>
                        Preview Recipients
                    </button>
                    
                    <button onclick="sendWarningEmailsReal()" class="btn btn-warning" style="background: linear-gradient(135deg, var(--warning) 0%, #d97706 100%);">
                        <i class="fas fa-envelope"></i>
                        Send Warning Emails
                    </button>
                </div>
                
                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                    <h5><i class="fas fa-info-circle"></i> Email System Status</h5>
                    <div id="email-status-display">
                        <p>Email system ready. Click "Send Test Email" to verify configuration.</p>
                    </div>
                </div>
            </div>

            <!-- Testing Tools -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <div class="card-icon" style="background: var(--gradient-success);">
                            <i class="fas fa-fingerprint"></i>
                        </div>
                        Testing Tools
                    </h3>
                </div>
                <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
                    <button onclick="simulateExamFingerprint('eligible')" class="btn btn-success">
                        <i class="fas fa-check"></i>
                        Simulate Eligible Student
                    </button>
                    <button onclick="simulateExamFingerprint('ineligible')" class="btn btn-danger">
                        <i class="fas fa-times"></i>
                        Simulate Ineligible Student
                    </button>
                </div>
            </div>

            <!-- Student Eligibility Status -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <div class="card-icon" style="background: var(--gradient-primary);">
                            <i class="fas fa-users"></i>
                        </div>
                        Student Eligibility Status
                    </h3>
                    <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
                        <button onclick="checkAllEligibility()" class="btn btn-primary">
                            <i class="fas fa-check-double"></i>
                            Check All Eligibility
                        </button>
                        <button onclick="exportEligibilityReport()" class="btn btn-success">
                            <i class="fas fa-download"></i>
                            Export Report
                        </button>
                    </div>
                </div>
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Student Name</th>
                                <th>Email</th>
                                <th>Course</th>
                                <th>Attendance %</th>
                                <th>Payment Status</th>
                                <th>Exam Eligibility</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="eligibility-data">
                            <!-- Populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </main>
    </div>

    <script src="assets/js/shared.js"></script>
    <script>
        // Exam management variables
        let examSession = {
            active: false,
            startTime: null,
            courseName: '',
            examDate: '',
            examTime: ''
        };

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadNavigation();
            initializeExamForm();
            loadExamData();
            
            // Refresh data every 30 seconds
            setInterval(refreshExamData, 30000);
        });

        // Load navigation component
        async function loadNavigation() {
            try {
                const response = await fetch('components/navigation.html');
                const html = await response.text();
                document.getElementById('navigation-placeholder').innerHTML = html;
            } catch (error) {
                console.error('Error loading navigation:', error);
            }
        }

        // Initialize exam form
        function initializeExamForm() {
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('exam-date').value = today;
            
            const now = new Date();
            const timeString = now.toTimeString().slice(0, 5);
            document.getElementById('exam-time').value = timeString;
        }

        // Load exam data
        async function loadExamData() {
            try {
                await Promise.all([
                    loadExamStats(),
                    loadEligibilityData()
                ]);
            } catch (error) {
                console.error('Error loading exam data:', error);
                AttendanceSystem.showNotification('Error loading exam data', 'error');
            }
        }

        // Load exam statistics
        async function loadExamStats() {
            try {
                const response = await AttendanceSystem.apiRequest('api/exam.php?action=stats');
                
                if (response.success) {
                    document.getElementById('eligible-count').textContent = response.eligible || 0;
                    document.getElementById('ineligible-count').textContent = response.ineligible || 0;
                    document.getElementById('checked-in-count').textContent = response.checked_in || 0;
                    
                    // Update exam status
                    const statusElement = document.getElementById('exam-status');
                    const durationElement = document.getElementById('exam-duration');
                    
                    if (response.exam_active) {
                        statusElement.textContent = 'Active';
                        durationElement.textContent = `Started: ${AttendanceSystem.formatTime(response.start_time)}`;
                        examSession.active = true;
                        updateExamButtons();
                    } else {
                        statusElement.textContent = 'Not Started';
                        durationElement.textContent = 'No active exam';
                        examSession.active = false;
                        updateExamButtons();
                    }
                }
            } catch (error) {
                console.error('Error loading exam stats:', error);
            }
        }

        // Load eligibility data
        async function loadEligibilityData() {
            try {
                const response = await AttendanceSystem.apiRequest('api/exam.php?action=eligibility');
                
                if (response.success && response.students) {
                    const columns = [
                        'name',
                        'email',
                        'course',
                        { key: 'attendance_percentage', render: (value) => `${value || 0}%` },
                        { key: 'payment_status', render: (value) => AttendanceSystem.createBadge(value || 'Unpaid', value === 'Paid' ? 'paid' : 'unpaid') },
                        { key: 'exam_eligible', render: (value) => AttendanceSystem.createBadge(value ? 'Eligible' : 'Ineligible', value ? 'eligible' : 'ineligible') },
                        { key: 'actions', render: (value, row) => createEligibilityActions(row) }
                    ];
                    
                    AttendanceSystem.populateTable('eligibility-data', response.students, columns);
                }
            } catch (error) {
                console.error('Error loading eligibility data:', error);
            }
        }

        // Create eligibility action buttons
        function createEligibilityActions(row) {
            const buttons = [];
            
            if (!row.exam_eligible) {
                buttons.push(`<button onclick="overrideEligibility('${row.id}', true)" class="btn btn-success" style="padding: 0.5rem; min-width: auto;" title="Override to Eligible">
                    <i class="fas fa-check"></i>
                </button>`);
            } else {
                buttons.push(`<button onclick="overrideEligibility('${row.id}', false)" class="btn btn-danger" style="padding: 0.5rem; min-width: auto;" title="Override to Ineligible">
                    <i class="fas fa-times"></i>
                </button>`);
            }
            
            buttons.push(`<button onclick="sendEligibilityEmail('${row.id}')" class="btn btn-primary" style="padding: 0.5rem; min-width: auto;" title="Send Email">
                <i class="fas fa-envelope"></i>
            </button>`);
            
            return `<div style="display: flex; gap: 0.25rem;">${buttons.join('')}</div>`;
        }

        // Setup exam
        async function setupExam() {
            const courseName = document.getElementById('course-name').value.trim();
            const examDate = document.getElementById('exam-date').value;
            const examTime = document.getElementById('exam-time').value;
            
            if (!courseName || !examDate || !examTime) {
                AttendanceSystem.showNotification('Please fill in all exam details', 'error');
                return;
            }
            
            try {
                const response = await AttendanceSystem.apiRequest('api/exam.php', {
                    method: 'POST',
                    body: JSON.stringify({
                        action: 'setup',
                        course_name: courseName,
                        exam_date: examDate,
                        exam_time: examTime
                    })
                });
                
                if (response.success) {
                    AttendanceSystem.showNotification('Exam setup completed successfully', 'success');
                    examSession.courseName = courseName;
                    examSession.examDate = examDate;
                    examSession.examTime = examTime;
                    updateExamButtons();
                    loadExamData();
                } else {
                    AttendanceSystem.showNotification(response.message || 'Failed to setup exam', 'error');
                }
            } catch (error) {
                console.error('Error setting up exam:', error);
                AttendanceSystem.showNotification('Failed to setup exam', 'error');
            }
        }

        // Start exam session
        async function startExamSession() {
            try {
                const response = await AttendanceSystem.apiRequest('api/exam.php', {
                    method: 'POST',
                    body: JSON.stringify({
                        action: 'start_session'
                    })
                });
                
                if (response.success) {
                    AttendanceSystem.showNotification('Exam session started', 'success');
                    examSession.active = true;
                    examSession.startTime = new Date();
                    updateExamButtons();
                    loadExamData();
                } else {
                    AttendanceSystem.showNotification(response.message || 'Failed to start exam session', 'error');
                }
            } catch (error) {
                console.error('Error starting exam session:', error);
                AttendanceSystem.showNotification('Failed to start exam session', 'error');
            }
        }

        // End exam session
        async function endExamSession() {
            if (!confirm('Are you sure you want to end the exam session? This action cannot be undone.')) {
                return;
            }
            
            try {
                const response = await AttendanceSystem.apiRequest('api/exam.php', {
                    method: 'POST',
                    body: JSON.stringify({
                        action: 'end_session'
                    })
                });
                
                if (response.success) {
                    AttendanceSystem.showNotification('Exam session ended', 'success');
                    examSession.active = false;
                    examSession.startTime = null;
                    updateExamButtons();
                    loadExamData();
                } else {
                    AttendanceSystem.showNotification(response.message || 'Failed to end exam session', 'error');
                }
            } catch (error) {
                console.error('Error ending exam session:', error);
                AttendanceSystem.showNotification('Failed to end exam session', 'error');
            }
        }

        // Update exam control buttons
        function updateExamButtons() {
            const setupBtn = document.getElementById('setup-exam-btn');
            const startBtn = document.getElementById('start-exam-btn');
            const endBtn = document.getElementById('end-exam-btn');
            
            if (examSession.active) {
                setupBtn.disabled = true;
                startBtn.disabled = true;
                endBtn.disabled = false;
            } else if (examSession.courseName) {
                setupBtn.disabled = false;
                startBtn.disabled = false;
                endBtn.disabled = true;
            } else {
                setupBtn.disabled = false;
                startBtn.disabled = true;
                endBtn.disabled = true;
            }
        }

        // Override student eligibility
        async function overrideEligibility(studentId, eligible) {
            try {
                const response = await AttendanceSystem.apiRequest('api/exam.php', {
                    method: 'POST',
                    body: JSON.stringify({
                        action: 'override_eligibility',
                        student_id: studentId,
                        eligible: eligible
                    })
                });
                
                if (response.success) {
                    AttendanceSystem.showNotification(`Student eligibility ${eligible ? 'granted' : 'revoked'}`, 'success');
                    loadExamData();
                } else {
                    AttendanceSystem.showNotification(response.message || 'Failed to update eligibility', 'error');
                }
            } catch (error) {
                console.error('Error updating eligibility:', error);
                AttendanceSystem.showNotification('Failed to update eligibility', 'error');
            }
        }

        // Send eligibility email to specific student
        async function sendEligibilityEmail(studentId) {
            try {
                const response = await AttendanceSystem.apiRequest('api/exam.php', {
                    method: 'POST',
                    body: JSON.stringify({
                        action: 'send_eligibility_email',
                        student_id: studentId
                    })
                });
                
                if (response.success) {
                    AttendanceSystem.showNotification('Eligibility email sent', 'success');
                } else {
                    AttendanceSystem.showNotification(response.message || 'Failed to send email', 'error');
                }
            } catch (error) {
                console.error('Error sending email:', error);
                AttendanceSystem.showNotification('Failed to send email', 'error');
            }
        }

        // Check all eligibility
        async function checkAllEligibility() {
            try {
                const response = await AttendanceSystem.apiRequest('api/exam.php', {
                    method: 'POST',
                    body: JSON.stringify({
                        action: 'check_all_eligibility'
                    })
                });
                
                if (response.success) {
                    AttendanceSystem.showNotification('Eligibility check completed for all students', 'success');
                    loadExamData();
                } else {
                    AttendanceSystem.showNotification(response.message || 'Failed to check eligibility', 'error');
                }
            } catch (error) {
                console.error('Error checking eligibility:', error);
                AttendanceSystem.showNotification('Failed to check eligibility', 'error');
            }
        }

        // Export eligibility report
        function exportEligibilityReport() {
            const url = 'api/exam.php?action=export_eligibility';
            
            const link = document.createElement('a');
            link.href = url;
            link.download = `exam_eligibility_${new Date().toISOString().split('T')[0]}.csv`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            AttendanceSystem.showNotification('Eligibility report exported', 'success');
        }

        // Email functions (placeholders for existing functionality)
        function sendTestWarningEmail() {
            AttendanceSystem.showNotification('Test warning email sent', 'info');
        }

        function previewWarningEmails() {
            AttendanceSystem.showNotification('Email preview feature coming soon', 'info');
        }

        function sendWarningEmailsReal() {
            AttendanceSystem.showNotification('Warning emails sent to ineligible students', 'warning');
        }

        // Simulate exam fingerprint
        function simulateExamFingerprint(type) {
            const message = type === 'eligible' ? 
                'Simulated eligible student fingerprint scan' : 
                'Simulated ineligible student fingerprint scan';
            AttendanceSystem.showNotification(message, type === 'eligible' ? 'success' : 'error');
        }

        // Refresh exam data
        function refreshExamData() {
            loadExamData();
            AttendanceSystem.showNotification('Exam data refreshed', 'info');
        }

        // Mobile sidebar toggle
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            if (sidebar) {
                sidebar.classList.toggle('mobile-visible');
            }
        }
    </script>
</body>
</html>

<?php
/**
 * User Setup Script - Run this once to create users table and demo accounts
 */

// Include configuration
require_once __DIR__ . '/config/config.php';

echo "<!DOCTYPE html>
<html>
<head>
    <title>User Setup - Attendance System</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); max-width: 800px; }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 4px; margin: 10px 0; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto; }
        .btn { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; }
        .btn:hover { background: #0056b3; }
    </style>
</head>
<body>
<div class='container'>
<h1>🔧 Attendance System - User Setup</h1>";

try {
    $db = getDB();
    
    // Read and execute the SQL file
    $sql = file_get_contents(__DIR__ . '/setup_users.sql');
    
    if (!$sql) {
        throw new Exception("Could not read setup_users.sql file");
    }
    
    // Split SQL into individual statements
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    echo "<div class='info'>📋 <strong>Executing database setup...</strong></div>";
    
    $successCount = 0;
    $errorCount = 0;
    
    foreach ($statements as $statement) {
        if (empty($statement)) continue;
        
        try {
            $db->exec($statement);
            $successCount++;
            
            // Show what we're doing
            if (stripos($statement, 'CREATE TABLE') !== false) {
                preg_match('/CREATE TABLE.*?`(\w+)`/i', $statement, $matches);
                $tableName = $matches[1] ?? 'unknown';
                echo "<div class='success'>✅ Created table: <strong>$tableName</strong></div>";
            } elseif (stripos($statement, 'INSERT') !== false) {
                preg_match('/INSERT.*?INTO.*?`(\w+)`/i', $statement, $matches);
                $tableName = $matches[1] ?? 'unknown';
                echo "<div class='success'>✅ Inserted data into: <strong>$tableName</strong></div>";
            } elseif (stripos($statement, 'ALTER TABLE') !== false) {
                preg_match('/ALTER TABLE.*?`(\w+)`/i', $statement, $matches);
                $tableName = $matches[1] ?? 'unknown';
                echo "<div class='success'>✅ Updated table: <strong>$tableName</strong></div>";
            }
            
        } catch (Exception $e) {
            $errorCount++;
            echo "<div class='error'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</div>";
        }
    }
    
    echo "<div class='info'>📊 <strong>Setup Summary:</strong><br>";
    echo "✅ Successful operations: $successCount<br>";
    echo "❌ Errors: $errorCount</div>";
    
    if ($errorCount == 0) {
        echo "<div class='success'>🎉 <strong>Setup completed successfully!</strong></div>";
        
        echo "<div class='info'>
        <h3>🔐 Demo Login Credentials:</h3>
        <pre>
<strong>Username:</strong> admin
<strong>Password:</strong> admin123
<strong>Role:</strong> Administrator

<strong>Username:</strong> teacher  
<strong>Password:</strong> admin123
<strong>Role:</strong> Teacher

<strong>Username:</strong> <EMAIL>
<strong>Password:</strong> admin123
<strong>Role:</strong> Administrator
        </pre>
        </div>";
        
        echo "<div class='info'>
        <h3>📋 What was created:</h3>
        <ul>
            <li>✅ <strong>users</strong> table with 5 demo accounts</li>
            <li>✅ <strong>payments</strong> table for finance management</li>
            <li>✅ <strong>exams</strong> table for examination management</li>
            <li>✅ Updated <strong>attendance</strong> table structure</li>
            <li>✅ Updated <strong>students</strong> table structure</li>
            <li>✅ Sample data for testing</li>
        </ul>
        </div>";
        
        echo "<p><a href='auth/login.php' class='btn'>🚀 Go to Login Page</a></p>";
        
    } else {
        echo "<div class='error'>⚠️ <strong>Setup completed with errors.</strong> Please check the error messages above.</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ <strong>Setup failed:</strong> " . htmlspecialchars($e->getMessage()) . "</div>";
    echo "<div class='info'>💡 <strong>Troubleshooting:</strong><br>";
    echo "1. Make sure your database 'attendance_system' exists<br>";
    echo "2. Check your database connection in config/database.php<br>";
    echo "3. Ensure MySQL is running<br>";
    echo "4. Verify database user permissions</div>";
}

echo "<hr>
<p><small>💡 <strong>Note:</strong> You can delete this setup file (setup_users.php) after successful setup for security.</small></p>
</div>
</body>
</html>";
?>

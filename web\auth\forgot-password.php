<?php
require_once __DIR__ . '/../config/config.php';

// Redirect if already logged in
if (isLoggedIn()) {
    header('Location: ' . BASE_URL . 'dashboard.php');
    exit();
}

$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $email = sanitizeInput($_POST['email'] ?? '');
    
    if (empty($email)) {
        $message = 'Please enter your email address.';
        $messageType = 'danger';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $message = 'Please enter a valid email address.';
        $messageType = 'danger';
    } else {
        try {
            $db = getDB();
            
            // Check if user exists
            $stmt = $db->prepare("SELECT id, username, email FROM users WHERE email = ? AND status = 'active'");
            $stmt->execute([$email]);
            $user = $stmt->fetch();
            
            if ($user) {
                // Generate reset token
                $token = bin2hex(random_bytes(32));
                $expires = date('Y-m-d H:i:s', strtotime('+1 hour'));
                
                // Store reset token in database
                $stmt = $db->prepare("
                    INSERT INTO password_resets (user_id, token, expires_at) 
                    VALUES (?, ?, ?)
                    ON DUPLICATE KEY UPDATE 
                    token = VALUES(token), 
                    expires_at = VALUES(expires_at),
                    created_at = NOW()
                ");
                $stmt->execute([$user['id'], $token, $expires]);
                
                // Create reset link
                $resetLink = BASE_URL . "auth/reset-password.php?token=" . $token;
                
                // Send email (you can implement actual email sending here)
                $emailSent = sendPasswordResetEmail($user['email'], $user['username'], $resetLink);
                
                if ($emailSent) {
                    $message = 'Password reset instructions have been sent to your email address.';
                    $messageType = 'success';
                } else {
                    $message = 'Failed to send reset email. Please try again later.';
                    $messageType = 'danger';
                }
            } else {
                // Don't reveal if email exists or not for security
                $message = 'If an account with that email exists, password reset instructions have been sent.';
                $messageType = 'info';
            }
            
        } catch (Exception $e) {
            error_log("Password reset error: " . $e->getMessage());
            $message = 'An error occurred. Please try again later.';
            $messageType = 'danger';
        }
    }
}

// Simple email function (you can replace with PHPMailer or other email service)
function sendPasswordResetEmail($email, $username, $resetLink) {
    $subject = "Password Reset - " . APP_NAME;
    $message = "
    <html>
    <head>
        <title>Password Reset</title>
    </head>
    <body>
        <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>
            <h2 style='color: #2563eb;'>Password Reset Request</h2>
            <p>Hello $username,</p>
            <p>You have requested to reset your password for " . APP_NAME . ".</p>
            <p>Click the link below to reset your password:</p>
            <p><a href='$resetLink' style='background: #2563eb; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Reset Password</a></p>
            <p>Or copy and paste this link in your browser:</p>
            <p>$resetLink</p>
            <p><strong>This link will expire in 1 hour.</strong></p>
            <p>If you did not request this password reset, please ignore this email.</p>
            <hr>
            <p><small>This is an automated message from " . APP_NAME . "</small></p>
        </div>
    </body>
    </html>
    ";
    
    $headers = "MIME-Version: 1.0" . "\r\n";
    $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
    $headers .= "From: " . APP_NAME . " <<EMAIL>>" . "\r\n";
    
    // For development, just log the email instead of sending
    if (defined('EMAIL_MODE') && EMAIL_MODE === 'simulation') {
        error_log("Password Reset Email for $email: $resetLink");
        return true;
    }
    
    return mail($email, $subject, $message, $headers);
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Forgot Password - <?php echo APP_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #2563eb 0%, #10b981 50%, #f59e0b 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        
        .reset-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .reset-icon {
            width: 64px;
            height: 64px;
            background: linear-gradient(135deg, #2563eb 0%, #10b981 100%);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            color: white;
            font-size: 1.5rem;
        }
        
        .reset-title {
            text-align: center;
            margin-bottom: 0.5rem;
            color: #1f2937;
            font-weight: 700;
            font-size: 1.5rem;
        }
        
        .reset-subtitle {
            text-align: center;
            margin-bottom: 2rem;
            color: #6b7280;
            font-size: 0.875rem;
        }
        
        .form-control {
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 0.75rem 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
        }
        
        .form-control:focus {
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }
        
        .btn-reset {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            border: none;
            border-radius: 8px;
            padding: 0.75rem 1rem;
            font-weight: 600;
            width: 100%;
            color: white;
        }
        
        .btn-reset:hover {
            background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
            color: white;
        }
        
        .back-link {
            text-align: center;
            margin-top: 1rem;
        }
        
        .back-link a {
            color: #6b7280;
            text-decoration: none;
            font-size: 0.875rem;
        }
        
        .back-link a:hover {
            color: #2563eb;
        }
    </style>
</head>
<body>
    <div class="reset-container">
        <div class="reset-icon">
            <i class="fas fa-key"></i>
        </div>
        
        <h1 class="reset-title">Forgot Password?</h1>
        <p class="reset-subtitle">Enter your email address and we'll send you instructions to reset your password.</p>
        
        <?php if ($message): ?>
            <div class="alert alert-<?php echo $messageType; ?>" role="alert">
                <i class="fas fa-<?php echo $messageType === 'success' ? 'check-circle' : ($messageType === 'danger' ? 'exclamation-circle' : 'info-circle'); ?> me-2"></i>
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>
        
        <form method="POST" action="">
            <div class="mb-3">
                <label for="email" class="form-label">Email Address</label>
                <input type="email" class="form-control" id="email" name="email" 
                       value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" 
                       placeholder="Enter your email address" required>
            </div>
            
            <button type="submit" class="btn btn-reset">
                <i class="fas fa-paper-plane me-2"></i>
                Send Reset Instructions
            </button>
        </form>
        
        <div class="back-link">
            <a href="login.php">
                <i class="fas fa-arrow-left me-1"></i>
                Back to Login
            </a>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

<?php
$pageTitle = 'Finance Management';
require_once __DIR__ . '/../includes/header.php';

// Handle payment submission
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['add_payment'])) {
    try {
        $db = getDB();
        
        $stmt = $db->prepare("
            INSERT INTO payments (student_id, amount, payment_type, payment_date, description, created_by) 
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            $_POST['student_id'],
            $_POST['amount'],
            $_POST['payment_type'],
            $_POST['payment_date'],
            $_POST['description'],
            $_SESSION['user_id']
        ]);
        
        setFlashMessage('success', 'Payment added successfully!');
        header('Location: ' . $_SERVER['REQUEST_URI']);
        exit();
        
    } catch (Exception $e) {
        error_log("Payment error: " . $e->getMessage());
        setFlashMessage('danger', 'Error adding payment. Please try again.');
    }
}

try {
    $db = getDB();
    
    // Get payment statistics
    $statsStmt = $db->query("
        SELECT 
            COUNT(*) as total_payments,
            COALESCE(SUM(amount), 0) as total_amount,
            COALESCE(SUM(CASE WHEN MONTH(payment_date) = MONTH(CURDATE()) THEN amount END), 0) as monthly_amount,
            COUNT(CASE WHEN MONTH(payment_date) = MONTH(CURDATE()) THEN 1 END) as monthly_payments
        FROM payments
    ");
    $stats = $statsStmt->fetch();
    
    // Get recent payments
    $paymentsStmt = $db->query("
        SELECT p.*, s.name as student_name, s.student_id 
        FROM payments p 
        JOIN students s ON p.student_id = s.id 
        ORDER BY p.payment_date DESC, p.created_at DESC 
        LIMIT 50
    ");
    $payments = $paymentsStmt->fetchAll();
    
    // Get students for dropdown
    $studentsStmt = $db->query("SELECT id, name, student_id FROM students WHERE status = 'active' ORDER BY name");
    $students = $studentsStmt->fetchAll();
    
} catch (Exception $e) {
    error_log("Finance page error: " . $e->getMessage());
    $stats = ['total_payments' => 0, 'total_amount' => 0, 'monthly_amount' => 0, 'monthly_payments' => 0];
    $payments = [];
    $students = [];
}
?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="page-title mb-0">Finance Management</h1>
        <p class="text-muted">Manage student payments and financial records</p>
    </div>
    <div>
        <button class="btn btn-outline-primary me-2" data-bs-toggle="modal" data-bs-target="#paymentModal">
            <i class="fas fa-chart-bar me-2"></i>Reports
        </button>
        <button class="btn btn-add" data-bs-toggle="modal" data-bs-target="#addPaymentModal">
            <i class="fas fa-plus me-2"></i>Add Payment
        </button>
    </div>
</div>

<!-- Finance Statistics -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="d-flex align-items-center">
                <div class="stats-icon" style="background: linear-gradient(135deg, var(--color-primary) 0%, var(--primary-light) 100%);">
                    <i class="fas fa-receipt"></i>
                </div>
                <div class="ms-3">
                    <div class="text-muted small">Total Payments</div>
                    <div class="h4 mb-0"><?php echo number_format($stats['total_payments']); ?></div>
                    <div class="small" style="color: var(--color-primary);">All time</div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="d-flex align-items-center">
                <div class="stats-icon" style="background: linear-gradient(135deg, var(--color-secondary) 0%, var(--secondary-light) 100%);">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <div class="ms-3">
                    <div class="text-muted small">Total Revenue</div>
                    <div class="h4 mb-0">$<?php echo number_format($stats['total_amount'], 2); ?></div>
                    <div class="small" style="color: var(--color-secondary);">All time</div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="d-flex align-items-center">
                <div class="stats-icon" style="background: linear-gradient(135deg, var(--color-accent) 0%, var(--accent-light) 100%);">
                    <i class="fas fa-calendar-alt"></i>
                </div>
                <div class="ms-3">
                    <div class="text-muted small">This Month</div>
                    <div class="h4 mb-0">$<?php echo number_format($stats['monthly_amount'], 2); ?></div>
                    <div class="small" style="color: var(--color-accent);"><?php echo $stats['monthly_payments']; ?> payments</div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="d-flex align-items-center">
                <div class="stats-icon" style="background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="ms-3">
                    <div class="text-muted small">Average Payment</div>
                    <div class="h4 mb-0">$<?php echo $stats['total_payments'] > 0 ? number_format($stats['total_amount'] / $stats['total_payments'], 2) : '0.00'; ?></div>
                    <div class="small text-muted">Per transaction</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Payments Table -->
<div class="table-card">
    <div class="table-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Recent Payments</h5>
        <div class="d-flex gap-2">
            <button class="btn btn-sm btn-outline-primary">
                <i class="fas fa-filter"></i>
                Filter
            </button>
            <button class="btn btn-sm btn-fresh">
                <i class="fas fa-sync"></i>
                Refresh
            </button>
        </div>
    </div>
    <div class="table-responsive">
        <table class="table table-hover mb-0">
            <thead class="table-light">
                <tr>
                    <th>Student</th>
                    <th>Amount</th>
                    <th>Type</th>
                    <th>Date</th>
                    <th>Description</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($payments)): ?>
                    <tr>
                        <td colspan="6" class="text-center text-muted py-4">No payments found</td>
                    </tr>
                <?php else: ?>
                    <?php foreach ($payments as $payment): ?>
                        <tr>
                            <td>
                                <div class="fw-medium"><?php echo htmlspecialchars($payment['student_name']); ?></div>
                                <div class="small text-muted"><?php echo htmlspecialchars($payment['student_id']); ?></div>
                            </td>
                            <td class="fw-medium text-success">$<?php echo number_format($payment['amount'], 2); ?></td>
                            <td>
                                <span class="badge bg-primary"><?php echo ucfirst($payment['payment_type']); ?></span>
                            </td>
                            <td><?php echo date('M j, Y', strtotime($payment['payment_date'])); ?></td>
                            <td><?php echo htmlspecialchars($payment['description'] ?? '-'); ?></td>
                            <td>
                                <div class="d-flex gap-1">
                                    <button class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-secondary">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>

<!-- Add Payment Modal -->
<div class="modal fade" id="addPaymentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Payment</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="student_id" class="form-label">Student</label>
                        <select class="form-select" id="student_id" name="student_id" required>
                            <option value="">Select Student</option>
                            <?php foreach ($students as $student): ?>
                                <option value="<?php echo $student['id']; ?>">
                                    <?php echo htmlspecialchars($student['name'] . ' (' . $student['student_id'] . ')'); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="amount" class="form-label">Amount</label>
                        <input type="number" class="form-control" id="amount" name="amount" step="0.01" required>
                    </div>
                    <div class="mb-3">
                        <label for="payment_type" class="form-label">Payment Type</label>
                        <select class="form-select" id="payment_type" name="payment_type" required>
                            <option value="tuition">Tuition</option>
                            <option value="fees">Fees</option>
                            <option value="books">Books</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="payment_date" class="form-label">Payment Date</label>
                        <input type="date" class="form-control" id="payment_date" name="payment_date" value="<?php echo date('Y-m-d'); ?>" required>
                    </div>
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-clear" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="add_payment" class="btn btn-add">Add Payment</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/../includes/footer.php'; ?>

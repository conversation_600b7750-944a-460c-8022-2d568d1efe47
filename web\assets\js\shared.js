// Shared JavaScript Functions for Attendance System

// Global variables
let esp32Status = 'offline';
let currentMode = 'Unknown';
let lastUpdate = 'Never';

// Initialize shared functionality
document.addEventListener('DOMContentLoaded', function() {
    initializeNavigation();
    initializeSystemStatus();
    initializeMobileMenu();
    checkESP32Status();
    
    // Check ESP32 status every 5 seconds
    setInterval(checkESP32Status, 5000);
});

// Navigation Management
function initializeNavigation() {
    // Set active navigation item based on current page
    const currentPage = window.location.pathname.split('/').pop() || 'dashboard.html';
    const navLinks = document.querySelectorAll('.nav-link');
    
    navLinks.forEach(link => {
        link.classList.remove('active');
        const href = link.getAttribute('href');
        if (href && href.includes(currentPage.replace('.html', ''))) {
            link.classList.add('active');
        }
    });
}

// Mobile menu functionality
function initializeMobileMenu() {
    const mobileToggle = document.querySelector('.mobile-toggle');
    const sidebar = document.querySelector('.sidebar');
    
    if (mobileToggle && sidebar) {
        mobileToggle.addEventListener('click', function() {
            sidebar.classList.toggle('mobile-visible');
        });
        
        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(e) {
            if (window.innerWidth <= 768) {
                if (!sidebar.contains(e.target) && !mobileToggle.contains(e.target)) {
                    sidebar.classList.remove('mobile-visible');
                }
            }
        });
    }
}

// System Status Management
function initializeSystemStatus() {
    updateSystemStatus();
}

function updateSystemStatus() {
    const esp32Dot = document.getElementById('esp32-dot');
    const esp32StatusText = document.getElementById('esp32-status');
    const lastUpdateText = document.getElementById('last-update');
    const currentModeText = document.getElementById('current-mode');
    
    if (esp32Dot) {
        esp32Dot.className = `status-dot ${esp32Status}`;
    }
    
    if (esp32StatusText) {
        esp32StatusText.textContent = `ESP32: ${esp32Status.charAt(0).toUpperCase() + esp32Status.slice(1)}`;
    }
    
    if (lastUpdateText) {
        lastUpdateText.textContent = lastUpdate;
    }
    
    if (currentModeText) {
        currentModeText.textContent = currentMode;
    }
}

// ESP32 Status Check
async function checkESP32Status() {
    try {
        const response = await fetch('api/system.php?action=status');
        const data = await response.json();
        
        if (data.success) {
            esp32Status = data.esp32_status || 'offline';
            currentMode = data.current_mode || 'Unknown';
            lastUpdate = new Date().toLocaleTimeString();
        } else {
            esp32Status = 'offline';
        }
    } catch (error) {
        console.error('Error checking ESP32 status:', error);
        esp32Status = 'offline';
    }
    
    updateSystemStatus();
}

// Notification System
function showNotification(message, type = 'info', duration = 5000) {
    // Remove existing notifications
    const existingNotifications = document.querySelectorAll('.notification');
    existingNotifications.forEach(notification => notification.remove());
    
    // Create new notification
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <i class="fas fa-${getNotificationIcon(type)}"></i>
        <span>${message}</span>
    `;
    
    document.body.appendChild(notification);
    
    // Show notification
    setTimeout(() => {
        notification.classList.add('show');
    }, 100);
    
    // Hide notification after duration
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, duration);
}

function getNotificationIcon(type) {
    switch (type) {
        case 'success': return 'check-circle';
        case 'error': return 'exclamation-circle';
        case 'warning': return 'exclamation-triangle';
        case 'info':
        default: return 'info-circle';
    }
}

// Loading State Management
function setButtonLoading(buttonId, loading = true) {
    const button = document.getElementById(buttonId);
    if (button) {
        if (loading) {
            button.classList.add('btn-loading');
            button.disabled = true;
        } else {
            button.classList.remove('btn-loading');
            button.disabled = false;
        }
    }
}

// Form Validation Helpers
function validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function validateRequired(value) {
    return value && value.trim().length > 0;
}

// Date/Time Formatting
function formatDateTime(date) {
    if (!date) return 'N/A';
    const d = new Date(date);
    return d.toLocaleString();
}

function formatTime(date) {
    if (!date) return 'N/A';
    const d = new Date(date);
    return d.toLocaleTimeString();
}

function formatDate(date) {
    if (!date) return 'N/A';
    const d = new Date(date);
    return d.toLocaleDateString();
}

// API Helper Functions
async function apiRequest(url, options = {}) {
    try {
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
            },
        };
        
        const response = await fetch(url, { ...defaultOptions, ...options });
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.message || 'API request failed');
        }
        
        return data;
    } catch (error) {
        console.error('API request error:', error);
        throw error;
    }
}

// Local Storage Helpers
function saveToStorage(key, data) {
    try {
        localStorage.setItem(key, JSON.stringify(data));
    } catch (error) {
        console.error('Error saving to storage:', error);
    }
}

function loadFromStorage(key) {
    try {
        const data = localStorage.getItem(key);
        return data ? JSON.parse(data) : null;
    } catch (error) {
        console.error('Error loading from storage:', error);
        return null;
    }
}

// Table Helper Functions
function createTableRow(data, columns) {
    const row = document.createElement('tr');
    
    columns.forEach(column => {
        const cell = document.createElement('td');
        
        if (typeof column === 'string') {
            cell.textContent = data[column] || 'N/A';
        } else if (typeof column === 'object') {
            if (column.render) {
                cell.innerHTML = column.render(data[column.key], data);
            } else {
                cell.textContent = data[column.key] || 'N/A';
            }
        }
        
        row.appendChild(cell);
    });
    
    return row;
}

function populateTable(tableBodyId, data, columns) {
    const tbody = document.getElementById(tableBodyId);
    if (!tbody) return;
    
    tbody.innerHTML = '';
    
    if (!data || data.length === 0) {
        const row = document.createElement('tr');
        const cell = document.createElement('td');
        cell.colSpan = columns.length;
        cell.textContent = 'No data available';
        cell.style.textAlign = 'center';
        cell.style.padding = '2rem';
        cell.style.color = 'var(--gray-500)';
        row.appendChild(cell);
        tbody.appendChild(row);
        return;
    }
    
    data.forEach(item => {
        const row = createTableRow(item, columns);
        tbody.appendChild(row);
    });
}

// Badge Helper Functions
function createBadge(text, type) {
    return `<span class="badge badge-${type}">${text}</span>`;
}

// Activity Feed Helper
function addActivityItem(feedId, icon, text, iconColor = 'var(--gradient-primary)') {
    const feed = document.getElementById(feedId);
    if (!feed) return;
    
    const item = document.createElement('div');
    item.className = 'activity-item';
    item.innerHTML = `
        <div class="activity-icon" style="background: ${iconColor};">
            <i class="fas fa-${icon}"></i>
        </div>
        <div class="activity-content">
            <div class="activity-text">${text}</div>
            <div class="activity-time">${new Date().toLocaleTimeString()}</div>
        </div>
    `;
    
    // Add to top of feed
    feed.insertBefore(item, feed.firstChild);
    
    // Limit to 10 items
    const items = feed.querySelectorAll('.activity-item');
    if (items.length > 10) {
        items[items.length - 1].remove();
    }
}

// Utility Functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function generateId() {
    return Math.random().toString(36).substr(2, 9);
}

// Export functions for use in other scripts
window.AttendanceSystem = {
    showNotification,
    setButtonLoading,
    validateEmail,
    validateRequired,
    formatDateTime,
    formatTime,
    formatDate,
    apiRequest,
    saveToStorage,
    loadFromStorage,
    populateTable,
    createBadge,
    addActivityItem,
    debounce,
    generateId,
    checkESP32Status,
    updateSystemStatus
};

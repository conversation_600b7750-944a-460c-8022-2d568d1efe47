<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Finance Management - Advanced Attendance System</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/shared.css" rel="stylesheet">
</head>
<body>
    <div class="layout-container">
        <!-- Navigation will be loaded here -->
        <div id="navigation-placeholder"></div>

        <!-- Main Content Area -->
        <main class="main-content" id="main-content">
            <!-- Mobile Header -->
            <div class="mobile-header">
                <div class="mobile-header-content">
                    <button class="mobile-toggle" onclick="toggleSidebar()">
                        <i class="fas fa-bars"></i>
                    </button>
                    <div class="logo">
                        <div class="logo-icon">
                            <i class="fas fa-fingerprint"></i>
                        </div>
                        Attendance System
                    </div>
                </div>
            </div>

            <!-- Page Header -->
            <div class="page-header">
                <h1 class="page-title">
                    <i class="fas fa-credit-card"></i>
                    Finance Management
                </h1>
                <p class="page-subtitle">Manage student payments and exam fee requirements</p>
            </div>

            <!-- Finance Statistics -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">Total Revenue</div>
                        <div class="stat-icon present">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                    </div>
                    <div class="stat-value" id="total-revenue">0 FRW</div>
                    <div class="stat-change">Total collected</div>
                </div>
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">Paid Students</div>
                        <div class="stat-icon present">
                            <i class="fas fa-check-circle"></i>
                        </div>
                    </div>
                    <div class="stat-value" id="paid-students">0</div>
                    <div class="stat-change">Students paid in full</div>
                </div>
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">Pending Payments</div>
                        <div class="stat-icon absent">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                    </div>
                    <div class="stat-value" id="pending-payments">0</div>
                    <div class="stat-change">Students with outstanding balance</div>
                </div>
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">Outstanding Amount</div>
                        <div class="stat-icon absent">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                    </div>
                    <div class="stat-value" id="outstanding-amount">0 FRW</div>
                    <div class="stat-change">Total outstanding</div>
                </div>
            </div>

            <!-- Payment Settings -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <div class="card-icon" style="background: var(--gradient-primary);">
                            <i class="fas fa-cog"></i>
                        </div>
                        Payment Settings
                    </h3>
                </div>
                <div style="display: flex; gap: 1rem; align-items: end; flex-wrap: wrap;">
                    <div class="form-group" style="margin-bottom: 0;">
                        <label class="form-label" for="global-threshold">
                            <i class="fas fa-dollar-sign"></i>
                            Global Exam Fee (FRW)
                        </label>
                        <input type="number" id="global-threshold" class="form-input" value="100" min="0" style="width: 200px;">
                    </div>
                    <button onclick="updateGlobalThreshold()" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        Update All Students
                    </button>
                    <button onclick="sendPaymentReminders()" class="btn btn-warning" style="background: linear-gradient(135deg, var(--warning) 0%, #d97706 100%);">
                        <i class="fas fa-envelope"></i>
                        Send Reminders
                    </button>
                </div>
            </div>

            <!-- Student Payment Status -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <div class="card-icon" style="background: var(--gradient-success);">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        Student Payment Status
                    </h3>
                    <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
                        <button onclick="markAllPaid()" class="btn btn-success">
                            <i class="fas fa-check"></i>
                            Mark All as Paid
                        </button>
                        <button onclick="markAllUnpaid()" class="btn btn-danger">
                            <i class="fas fa-times"></i>
                            Mark All as Unpaid
                        </button>
                        <button onclick="generatePaymentReport()" class="btn btn-primary">
                            <i class="fas fa-download"></i>
                            Export Report
                        </button>
                        <button onclick="refreshFinanceData()" class="btn btn-info" style="background: linear-gradient(135deg, var(--info) 0%, #0891b2 100%);">
                            <i class="fas fa-refresh"></i>
                            Refresh
                        </button>
                    </div>
                </div>
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Student Name</th>
                                <th>Email</th>
                                <th>Required Amount</th>
                                <th>Amount Paid</th>
                                <th>Balance</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="finance-data">
                            <!-- Populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Payment History and Analytics -->
            <div class="form-grid">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <div class="card-icon" style="background: var(--gradient-primary);">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            Payment Analytics
                        </h3>
                    </div>
                    <div id="payment-analytics">
                        <div style="text-align: center; padding: 2rem; color: var(--gray-500);">
                            <i class="fas fa-chart-pie" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                            <p>Payment analytics will be displayed here</p>
                            <div style="margin-top: 1rem;">
                                <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                                    <span>Collection Rate:</span>
                                    <span id="collection-rate">0%</span>
                                </div>
                                <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                                    <span>Average Payment:</span>
                                    <span id="average-payment">0 FRW</span>
                                </div>
                                <div style="display: flex; justify-content: space-between;">
                                    <span>Payment Completion:</span>
                                    <span id="payment-completion">0%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <div class="card-icon" style="background: var(--gradient-success);">
                                <i class="fas fa-history"></i>
                            </div>
                            Recent Transactions
                        </h3>
                    </div>
                    <div class="activity-feed" id="transaction-feed">
                        <div class="activity-item">
                            <div class="activity-icon" style="background: var(--gradient-primary);">
                                <i class="fas fa-power-off"></i>
                            </div>
                            <div class="activity-content">
                                <div class="activity-text">Finance system initialized</div>
                                <div class="activity-time">Just now</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="assets/js/shared.js"></script>
    <script>
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadNavigation();
            loadFinanceData();
            
            // Refresh data every 60 seconds
            setInterval(refreshFinanceData, 60000);
        });

        // Load navigation component
        async function loadNavigation() {
            try {
                const response = await fetch('components/navigation.html');
                const html = await response.text();
                document.getElementById('navigation-placeholder').innerHTML = html;
            } catch (error) {
                console.error('Error loading navigation:', error);
            }
        }

        // Load finance data
        async function loadFinanceData() {
            try {
                await Promise.all([
                    loadFinanceStats(),
                    loadPaymentData(),
                    loadPaymentAnalytics()
                ]);
            } catch (error) {
                console.error('Error loading finance data:', error);
                AttendanceSystem.showNotification('Error loading finance data', 'error');
            }
        }

        // Load finance statistics
        async function loadFinanceStats() {
            try {
                const response = await AttendanceSystem.apiRequest('api/finance.php?action=stats');
                
                if (response.success) {
                    document.getElementById('total-revenue').textContent = `${response.total_revenue || 0} FRW`;
                    document.getElementById('paid-students').textContent = response.paid_students || 0;
                    document.getElementById('pending-payments').textContent = response.pending_payments || 0;
                    document.getElementById('outstanding-amount').textContent = `${response.outstanding_amount || 0} FRW`;
                }
            } catch (error) {
                console.error('Error loading finance stats:', error);
            }
        }

        // Load payment data
        async function loadPaymentData() {
            try {
                const response = await AttendanceSystem.apiRequest('api/finance.php?action=list');
                
                if (response.success && response.payments) {
                    const columns = [
                        'student_name',
                        'email',
                        { key: 'required_amount', render: (value) => `${value || 0} FRW` },
                        { key: 'amount_paid', render: (value) => `${value || 0} FRW` },
                        { key: 'balance', render: (value) => `${value || 0} FRW` },
                        { key: 'status', render: (value) => AttendanceSystem.createBadge(value || 'Unpaid', value === 'Paid' ? 'paid' : 'unpaid') },
                        { key: 'actions', render: (value, row) => createPaymentActions(row) }
                    ];
                    
                    AttendanceSystem.populateTable('finance-data', response.payments, columns);
                }
            } catch (error) {
                console.error('Error loading payment data:', error);
            }
        }

        // Load payment analytics
        async function loadPaymentAnalytics() {
            try {
                const response = await AttendanceSystem.apiRequest('api/finance.php?action=analytics');
                
                if (response.success) {
                    document.getElementById('collection-rate').textContent = `${response.collection_rate || 0}%`;
                    document.getElementById('average-payment').textContent = `${response.average_payment || 0} FRW`;
                    document.getElementById('payment-completion').textContent = `${response.payment_completion || 0}%`;
                }
            } catch (error) {
                console.error('Error loading payment analytics:', error);
            }
        }

        // Create payment action buttons
        function createPaymentActions(row) {
            const buttons = [];
            
            if (row.status !== 'Paid') {
                buttons.push(`<button onclick="recordPayment('${row.student_id}', ${row.balance})" class="btn btn-success" style="padding: 0.5rem; min-width: auto;" title="Record Payment">
                    <i class="fas fa-dollar-sign"></i>
                </button>`);
            }
            
            buttons.push(`<button onclick="editPayment('${row.student_id}')" class="btn btn-primary" style="padding: 0.5rem; min-width: auto;" title="Edit Payment">
                <i class="fas fa-edit"></i>
            </button>`);
            
            buttons.push(`<button onclick="sendPaymentReminder('${row.student_id}')" class="btn btn-warning" style="padding: 0.5rem; min-width: auto; background: linear-gradient(135deg, var(--warning) 0%, #d97706 100%);" title="Send Reminder">
                <i class="fas fa-envelope"></i>
            </button>`);
            
            return `<div style="display: flex; gap: 0.25rem;">${buttons.join('')}</div>`;
        }

        // Record payment
        async function recordPayment(studentId, amount) {
            const paymentAmount = prompt(`Enter payment amount (Balance: ${amount} FRW):`, amount);
            
            if (paymentAmount === null || paymentAmount === '') return;
            
            const numAmount = parseFloat(paymentAmount);
            if (isNaN(numAmount) || numAmount <= 0) {
                AttendanceSystem.showNotification('Please enter a valid payment amount', 'error');
                return;
            }
            
            try {
                const response = await AttendanceSystem.apiRequest('api/finance.php', {
                    method: 'POST',
                    body: JSON.stringify({
                        action: 'record_payment',
                        student_id: studentId,
                        amount: numAmount
                    })
                });
                
                if (response.success) {
                    AttendanceSystem.showNotification('Payment recorded successfully', 'success');
                    AttendanceSystem.addActivityItem('transaction-feed', 'dollar-sign', 
                        `Payment of ${numAmount} FRW recorded`, 'var(--gradient-success)');
                    loadFinanceData();
                } else {
                    AttendanceSystem.showNotification(response.message || 'Failed to record payment', 'error');
                }
            } catch (error) {
                console.error('Error recording payment:', error);
                AttendanceSystem.showNotification('Failed to record payment', 'error');
            }
        }

        // Edit payment
        function editPayment(studentId) {
            AttendanceSystem.showNotification('Edit payment feature coming soon', 'info');
        }

        // Send payment reminder to specific student
        async function sendPaymentReminder(studentId) {
            try {
                const response = await AttendanceSystem.apiRequest('api/finance.php', {
                    method: 'POST',
                    body: JSON.stringify({
                        action: 'send_reminder',
                        student_id: studentId
                    })
                });
                
                if (response.success) {
                    AttendanceSystem.showNotification('Payment reminder sent', 'success');
                    AttendanceSystem.addActivityItem('transaction-feed', 'envelope', 
                        'Payment reminder sent', 'var(--gradient-primary)');
                } else {
                    AttendanceSystem.showNotification(response.message || 'Failed to send reminder', 'error');
                }
            } catch (error) {
                console.error('Error sending reminder:', error);
                AttendanceSystem.showNotification('Failed to send reminder', 'error');
            }
        }

        // Update global threshold
        async function updateGlobalThreshold() {
            const threshold = document.getElementById('global-threshold').value;
            
            if (!threshold || threshold <= 0) {
                AttendanceSystem.showNotification('Please enter a valid amount', 'error');
                return;
            }
            
            try {
                const response = await AttendanceSystem.apiRequest('api/finance.php', {
                    method: 'POST',
                    body: JSON.stringify({
                        action: 'update_global_threshold',
                        amount: parseFloat(threshold)
                    })
                });
                
                if (response.success) {
                    AttendanceSystem.showNotification('Global exam fee updated for all students', 'success');
                    AttendanceSystem.addActivityItem('transaction-feed', 'cog', 
                        `Global exam fee updated to ${threshold} FRW`, 'var(--gradient-primary)');
                    loadFinanceData();
                } else {
                    AttendanceSystem.showNotification(response.message || 'Failed to update threshold', 'error');
                }
            } catch (error) {
                console.error('Error updating threshold:', error);
                AttendanceSystem.showNotification('Failed to update threshold', 'error');
            }
        }

        // Mark all as paid
        async function markAllPaid() {
            if (!confirm('Are you sure you want to mark all students as paid? This action cannot be undone.')) {
                return;
            }
            
            try {
                const response = await AttendanceSystem.apiRequest('api/finance.php', {
                    method: 'POST',
                    body: JSON.stringify({
                        action: 'mark_all_paid'
                    })
                });
                
                if (response.success) {
                    AttendanceSystem.showNotification('All students marked as paid', 'success');
                    AttendanceSystem.addActivityItem('transaction-feed', 'check-circle', 
                        'All students marked as paid', 'var(--gradient-success)');
                    loadFinanceData();
                } else {
                    AttendanceSystem.showNotification(response.message || 'Failed to update payments', 'error');
                }
            } catch (error) {
                console.error('Error marking all paid:', error);
                AttendanceSystem.showNotification('Failed to update payments', 'error');
            }
        }

        // Mark all as unpaid
        async function markAllUnpaid() {
            if (!confirm('Are you sure you want to mark all students as unpaid? This action cannot be undone.')) {
                return;
            }
            
            try {
                const response = await AttendanceSystem.apiRequest('api/finance.php', {
                    method: 'POST',
                    body: JSON.stringify({
                        action: 'mark_all_unpaid'
                    })
                });
                
                if (response.success) {
                    AttendanceSystem.showNotification('All students marked as unpaid', 'success');
                    AttendanceSystem.addActivityItem('transaction-feed', 'times-circle', 
                        'All students marked as unpaid', 'var(--gradient-error)');
                    loadFinanceData();
                } else {
                    AttendanceSystem.showNotification(response.message || 'Failed to update payments', 'error');
                }
            } catch (error) {
                console.error('Error marking all unpaid:', error);
                AttendanceSystem.showNotification('Failed to update payments', 'error');
            }
        }

        // Send payment reminders to all
        async function sendPaymentReminders() {
            try {
                const response = await AttendanceSystem.apiRequest('api/finance.php', {
                    method: 'POST',
                    body: JSON.stringify({
                        action: 'send_all_reminders'
                    })
                });
                
                if (response.success) {
                    AttendanceSystem.showNotification('Payment reminders sent to all students with outstanding balance', 'success');
                    AttendanceSystem.addActivityItem('transaction-feed', 'envelope', 
                        'Payment reminders sent to all', 'var(--gradient-warning)');
                } else {
                    AttendanceSystem.showNotification(response.message || 'Failed to send reminders', 'error');
                }
            } catch (error) {
                console.error('Error sending reminders:', error);
                AttendanceSystem.showNotification('Failed to send reminders', 'error');
            }
        }

        // Generate payment report
        function generatePaymentReport() {
            const url = 'api/finance.php?action=export';
            
            // Create a temporary link to download the file
            const link = document.createElement('a');
            link.href = url;
            link.download = `payment_report_${new Date().toISOString().split('T')[0]}.csv`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            AttendanceSystem.showNotification('Payment report exported', 'success');
        }

        // Refresh finance data
        function refreshFinanceData() {
            loadFinanceData();
            AttendanceSystem.showNotification('Finance data refreshed', 'info');
        }

        // Mobile sidebar toggle
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            if (sidebar) {
                sidebar.classList.toggle('mobile-visible');
            }
        }
    </script>
</body>
</html>

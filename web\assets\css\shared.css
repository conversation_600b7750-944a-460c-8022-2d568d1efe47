/* Shared CSS Variables and Base Styles for Attendance System */
:root {
    /* Three Main Colors Theme */
    --color-primary: #2563eb;      /* Professional Blue */
    --color-secondary: #10b981;    /* Success Green */
    --color-accent: #f59e0b;       /* Warning Orange */

    /* Color Variations */
    --primary-light: #3b82f6;
    --primary-dark: #1d4ed8;
    --secondary-light: #34d399;
    --secondary-dark: #059669;
    --accent-light: #fbbf24;
    --accent-dark: #d97706;

    /* Semantic Colors */
    --success: var(--color-secondary);
    --warning: var(--color-accent);
    --error: #ef4444;
    --info: var(--color-primary);
    --dark: #1e293b;
    --light: #f8fafc;

    /* Gray Scale */
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    --white: #ffffff;

    /* Shadows */
    --shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1);

    /* Gradients using Three Main Colors */
    --gradient-primary: linear-gradient(135deg, var(--color-primary) 0%, var(--primary-light) 100%);
    --gradient-secondary: linear-gradient(135deg, var(--color-secondary) 0%, var(--secondary-light) 100%);
    --gradient-accent: linear-gradient(135deg, var(--color-accent) 0%, var(--accent-light) 100%);
    --gradient-mixed: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 50%, var(--color-accent) 100%);
    --gradient-success: var(--gradient-secondary);
    --gradient-error: linear-gradient(135deg, var(--error) 0%, #dc2626 100%);

    /* Layout */
    --sidebar-width: 280px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    background: #ffffff;
    color: var(--gray-900);
    line-height: 1.6;
    min-height: 100vh;
    overflow-x: hidden;
}

/* Layout Container */
.layout-container {
    display: flex;
    min-height: 100vh;
}

/* Sidebar Navigation */
.sidebar {
    width: var(--sidebar-width);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-right: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-lg);
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    z-index: 1000;
    transition: transform 0.3s ease;
}

.sidebar.mobile-hidden {
    transform: translateX(-100%);
}

.sidebar-header {
    padding: 2rem 1.5rem;
    border-bottom: 1px solid var(--gray-200);
}

.logo {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: 1.3rem;
    font-weight: 800;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.logo-icon {
    width: 40px;
    height: 40px;
    background: var(--gradient-primary);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}

.system-status-sidebar {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--gray-200);
}

.status-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 12px;
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.status-dot.online { background: var(--success); }
.status-dot.offline { background: var(--error); animation: none; }
.status-dot.connecting { background: var(--warning); }

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.sidebar-nav {
    padding: 1rem 0;
}

.nav-item {
    margin: 0 1rem 0.5rem;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 1.5rem;
    color: var(--gray-600);
    text-decoration: none;
    border-radius: 12px;
    transition: all 0.3s ease;
    font-weight: 500;
    position: relative;
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    transition: left 0.3s ease;
    z-index: -1;
    opacity: 0.1;
}

.nav-link:hover::before,
.nav-link.active::before {
    left: 0;
}

.nav-link:hover,
.nav-link.active {
    color: var(--color-primary);
    background: rgba(37, 99, 235, 0.1);
    transform: translateX(5px);
}

.nav-link i {
    width: 20px;
    text-align: center;
}

/* Main Content */
.main-content {
    flex: 1;
    margin-left: var(--sidebar-width);
    min-height: 100vh;
    padding: 2rem;
    transition: margin-left 0.3s ease;
}

.main-content.sidebar-collapsed {
    margin-left: 0;
}

/* Mobile Header */
.mobile-header {
    display: none;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    padding: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow);
    margin: -2rem -2rem 2rem -2rem;
}

.mobile-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.mobile-toggle {
    background: var(--color-primary);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 0.5rem;
    cursor: pointer;
    font-size: 1.2rem;
}

/* Page Header */
.page-header {
    margin-bottom: 2rem;
    text-align: center;
}

.page-title {
    font-size: 2.5rem;
    font-weight: 800;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.5rem;
}

.page-subtitle {
    color: var(--gray-600);
    font-size: 1.1rem;
    max-width: 600px;
    margin: 0 auto;
}

/* Advanced Cards */
.card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: var(--shadow-xl);
    border: 1px solid rgba(255, 255, 255, 0.2);
    margin-bottom: 2rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--gray-100);
}

.card-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gray-900);
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.card-icon {
    width: 40px;
    height: 40px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: white;
}

/* Forms */
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: 0.5rem;
}

.form-input, .form-select, .form-control {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 0.875rem;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    background: white;
    font-family: inherit;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.form-input:focus, .form-select:focus, .form-control:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1), 0 1px 2px rgba(0, 0, 0, 0.05);
    transform: none;
}

.form-input:hover:not(:focus), .form-select:hover:not(:focus), .form-control:hover:not(:focus) {
    border-color: #9ca3af;
}

/* Advanced Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    text-decoration: none;
    position: relative;
    overflow: hidden;
    min-width: 120px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
    letter-spacing: 0.025em;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Outline Button Styles */
.btn-outline-primary {
    background: transparent;
    color: var(--color-primary);
    border: 1px solid var(--color-primary);
}

.btn-outline-primary:hover:not(:disabled) {
    background: var(--color-primary);
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

.btn-outline-secondary {
    background: transparent;
    color: var(--color-secondary);
    border: 1px solid var(--color-secondary);
}

.btn-outline-secondary:hover:not(:disabled) {
    background: var(--color-secondary);
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.btn-outline-danger {
    background: transparent;
    color: #dc2626;
    border: 1px solid #dc2626;
}

.btn-outline-danger:hover:not(:disabled) {
    background: #dc2626;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
}

/* Small Button Variant */
.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
    min-width: 80px;
}

/* Large Button Variant */
.btn-lg {
    padding: 1rem 2rem;
    font-size: 1rem;
    min-width: 160px;
}

.btn-primary {
    background: var(--color-primary);
    color: white;
    border: 1px solid var(--color-primary);
}

.btn-primary:hover:not(:disabled) {
    background: var(--primary-dark);
    border-color: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.4);
}

.btn-success {
    background: var(--color-secondary);
    color: white;
    border: 1px solid var(--color-secondary);
}

.btn-success:hover:not(:disabled) {
    background: var(--secondary-dark);
    border-color: var(--secondary-dark);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
}

.btn-danger, .btn-delete, .btn-clear, .btn-error {
    background: #dc2626;
    color: white;
    box-shadow: var(--shadow);
    border: none;
}

.btn-danger:hover:not(:disabled),
.btn-delete:hover:not(:disabled),
.btn-clear:hover:not(:disabled),
.btn-error:hover:not(:disabled) {
    background: #b91c1c;
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

.btn-add, .btn-fresh {
    background: var(--color-primary);
    color: white;
    border: 1px solid var(--color-primary);
}

.btn-add:hover:not(:disabled),
.btn-fresh:hover:not(:disabled) {
    background: var(--primary-dark);
    border-color: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.4);
}

.btn-loading {
    position: relative;
    color: transparent !important;
}

.btn-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    transform: translate(-50%, -50%);
}

@keyframes spin {
    from { transform: translate(-50%, -50%) rotate(0deg); }
    to { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Professional Stats Cards */
.stats-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
    border: 1px solid #e5e7eb;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
    border-color: #d1d5db;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--color-primary);
}

.stats-icon {
    width: 48px;
    height: 48px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: white;
    margin-bottom: 0.5rem;
}

/* Professional Table Cards */
.table-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
    border: 1px solid #e5e7eb;
    overflow: hidden;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.table-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06);
    border-color: #d1d5db;
}

.table-header {
    padding: 1.25rem 1.5rem;
    border-bottom: 1px solid #e5e7eb;
    background-color: #374151;
    color: white;
    position: relative;
}

.table-header h5 {
    color: white;
    margin: 0;
    font-weight: 600;
    font-size: 1rem;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: var(--shadow-lg);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-xl);
}

.stat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.stat-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--gray-600);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.stat-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: white;
}

.stat-icon.present { background: var(--gradient-secondary); }
.stat-icon.partial { background: var(--gradient-accent); }
.stat-icon.absent { background: var(--gradient-error); }
.stat-icon.total { background: var(--gradient-primary); }

.stat-value {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--gray-900);
    margin-bottom: 0.25rem;
}

.stat-change {
    font-size: 0.875rem;
    color: var(--gray-600);
    font-weight: 500;
}

/* Tables */
.table-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 16px;
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.table {
    width: 100%;
    border-collapse: collapse;
}

.table th {
    background: var(--gray-700) !important;
    color: white !important;
    padding: 1rem;
    text-align: left;
    font-size: 0.875rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border: none;
}

.table td {
    padding: 1rem;
    border-bottom: 1px solid var(--gray-200);
    font-size: 0.875rem;
}

.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background: rgba(55, 65, 81, 0.05);
}

.table tbody tr:last-child td {
    border-bottom: none;
}

/* Professional Badges */
.badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 500;
    letter-spacing: 0.025em;
    border: 1px solid transparent;
}

.badge.bg-success {
    background: rgba(16, 185, 129, 0.1) !important;
    color: var(--color-secondary) !important;
    border-color: rgba(16, 185, 129, 0.2);
}

.badge.bg-danger {
    background: rgba(239, 68, 68, 0.1) !important;
    color: #ef4444 !important;
    border-color: rgba(239, 68, 68, 0.2);
}

.badge.bg-warning {
    background: rgba(245, 158, 11, 0.1) !important;
    color: var(--color-accent) !important;
    border-color: rgba(245, 158, 11, 0.2);
}

.badge.bg-primary {
    background: rgba(37, 99, 235, 0.1) !important;
    color: var(--color-primary) !important;
    border-color: rgba(37, 99, 235, 0.2);
}

.badge-present { background: rgba(16, 185, 129, 0.1); color: var(--color-secondary); }
.badge-partial { background: rgba(245, 158, 11, 0.1); color: var(--color-accent); }
.badge-absent { background: rgba(239, 68, 68, 0.1); color: var(--error); }
.badge-paid { background: rgba(16, 185, 129, 0.1); color: var(--color-secondary); }
.badge-unpaid { background: rgba(239, 68, 68, 0.1); color: var(--error); }
.badge-eligible { background: rgba(16, 185, 129, 0.1); color: var(--color-secondary); }
.badge-ineligible { background: rgba(239, 68, 68, 0.1); color: var(--error); }
.badge-active { background: rgba(37, 99, 235, 0.1); color: var(--color-primary); }

/* Notifications */
.notification {
    position: fixed;
    top: 2rem;
    right: 2rem;
    padding: 1rem 1.5rem;
    border-radius: 12px;
    color: white;
    font-weight: 600;
    font-size: 0.875rem;
    z-index: 10000;
    transform: translateX(400px);
    transition: all 0.3s ease;
    box-shadow: var(--shadow-xl);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    max-width: 400px;
}

.notification.show {
    transform: translateX(0);
}

.notification.success { background: var(--gradient-secondary); }
.notification.error { background: var(--gradient-error); }
.notification.info { background: var(--gradient-primary); }
.notification.warning { background: var(--gradient-accent); }

/* Loading Spinner */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid rgba(37, 99, 235, 0.3);
    border-top: 2px solid var(--color-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Hardware Status Cards - Smaller Size */
.hardware-status {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.hardware-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 12px;
    padding: 1rem;
    text-align: center;
    box-shadow: var(--shadow);
    border: 2px solid transparent;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    max-width: 250px;
    margin: 0 auto;
}

.hardware-card.ready {
    border-color: var(--color-secondary);
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.05) 100%);
}

.hardware-card.waiting {
    border-color: var(--color-accent);
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(217, 119, 6, 0.05) 100%);
    animation: pulse-glow 2s infinite;
}

.hardware-card.error {
    border-color: var(--error);
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(220, 38, 38, 0.05) 100%);
}

@keyframes pulse-glow {
    0%, 100% { box-shadow: 0 0 20px rgba(245, 158, 11, 0.3); }
    50% { box-shadow: 0 0 40px rgba(245, 158, 11, 0.6); }
}

.hardware-icon {
    width: 50px;
    height: 50px;
    margin: 0 auto 0.75rem;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: white;
    position: relative;
}

.hardware-icon.rfid {
    background: var(--gradient-primary);
}

.hardware-icon.fingerprint {
    background: var(--gradient-accent);
}

.hardware-icon.scanning {
    animation: spin 2s linear infinite;
}

.hardware-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.375rem;
    color: var(--gray-900);
}

.hardware-status-text {
    font-size: 0.75rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.hardware-status-text.ready { color: var(--color-secondary); }
.hardware-status-text.waiting { color: var(--color-accent); }
.hardware-status-text.error { color: var(--error); }

.hardware-details {
    font-size: 0.625rem;
    color: var(--gray-600);
}

/* Real-time Activity Feed */
.activity-feed {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 16px;
    padding: 1.5rem;
    max-height: 400px;
    overflow-y: auto;
    box-shadow: var(--shadow-lg);
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-radius: 12px;
    margin-bottom: 0.5rem;
    transition: all 0.2s ease;
    border-left: 4px solid transparent;
}

.activity-item:hover {
    background: rgba(37, 99, 235, 0.05);
    border-left-color: var(--color-primary);
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    color: white;
    flex-shrink: 0;
}

.activity-content {
    flex: 1;
}

.activity-text {
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 0.25rem;
}

.activity-time {
    font-size: 0.75rem;
    color: var(--gray-500);
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
    }

    .sidebar.mobile-visible {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    .mobile-header {
        display: block;
    }

    .hardware-status {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .form-grid {
        grid-template-columns: 1fr;
    }

    .page-title {
        font-size: 2rem;
    }

    .card {
        padding: 1.5rem;
    }

    .card-header {
        flex-direction: column;
        gap: 1rem;
    }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--gray-100);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--gray-300);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gray-400);
}

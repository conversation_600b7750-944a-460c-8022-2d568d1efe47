-- Create users table for authentication
CREATE TABLE IF NOT EXISTS `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `role` enum('admin','teacher','accountant','staff') DEFAULT 'staff',
  `status` enum('active','inactive') DEFAULT 'active',
  `first_name` varchar(50) DEFAULT NULL,
  `last_name` varchar(50) DEFAULT NULL,
  `last_login` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY <PERSON>EY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`),
  <PERSON><PERSON><PERSON> `idx_username` (`username`),
  KEY `idx_email` (`email`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert demo users with hashed passwords
-- Password for all accounts: admin123
INSERT INTO `users` (`username`, `email`, `password`, `role`, `status`, `first_name`, `last_name`) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 'active', 'System', 'Administrator'),
('teacher', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'teacher', 'active', 'Demo', 'Teacher'),
('<EMAIL>', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 'active', 'INES', 'Admin'),
('<EMAIL>', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'teacher', 'active', 'Department', 'Head'),
('<EMAIL>', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'accountant', 'active', 'Finance', 'Officer');

-- Create payments table if it doesn't exist (for finance module)
CREATE TABLE IF NOT EXISTS `payments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `payment_type` enum('tuition','fees','books','other') DEFAULT 'tuition',
  `payment_date` date NOT NULL,
  `description` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_student` (`student_id`),
  KEY `idx_payment_date` (`payment_date`),
  KEY `idx_payment_type` (`payment_type`),
  KEY `idx_created_by` (`created_by`),
  CONSTRAINT `payments_ibfk_1` FOREIGN KEY (`student_id`) REFERENCES `students` (`student_id`) ON DELETE CASCADE,
  CONSTRAINT `payments_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create exams table if it doesn't exist (for examination module)
CREATE TABLE IF NOT EXISTS `exams` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(200) NOT NULL,
  `description` text DEFAULT NULL,
  `exam_date` datetime NOT NULL,
  `duration` int(11) NOT NULL COMMENT 'Duration in minutes',
  `total_marks` int(11) NOT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_exam_date` (`exam_date`),
  KEY `idx_created_by` (`created_by`),
  CONSTRAINT `exams_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Update attendance table to match the new structure expected by the PHP code
ALTER TABLE `attendance` 
ADD COLUMN IF NOT EXISTS `id` int(11) NOT NULL AUTO_INCREMENT FIRST,
ADD COLUMN IF NOT EXISTS `date` date NOT NULL AFTER `student_id`,
ADD COLUMN IF NOT EXISTS `time_in` time DEFAULT NULL AFTER `date`,
ADD COLUMN IF NOT EXISTS `time_out` time DEFAULT NULL AFTER `time_in`,
ADD COLUMN IF NOT EXISTS `marked_by` int(11) DEFAULT NULL AFTER `notes`,
DROP PRIMARY KEY,
ADD PRIMARY KEY (`id`),
ADD UNIQUE KEY `unique_student_date_new` (`student_id`, `date`),
ADD KEY `idx_marked_by` (`marked_by`),
ADD CONSTRAINT `attendance_marked_by_fk` FOREIGN KEY (`marked_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

-- Update students table to match expected structure
ALTER TABLE `students` 
CHANGE COLUMN `student_id` `id` int(11) NOT NULL AUTO_INCREMENT,
ADD COLUMN IF NOT EXISTS `student_id` varchar(20) NOT NULL AFTER `name`,
ADD COLUMN IF NOT EXISTS `class` varchar(50) DEFAULT NULL AFTER `course`,
ADD UNIQUE KEY `unique_student_id` (`student_id`);

-- Insert some sample students if none exist
INSERT IGNORE INTO `students` (`name`, `student_id`, `email`, `course`, `class`, `rfid_id`, `status`) VALUES
('John Doe', 'STU001', '<EMAIL>', 'Computer Science', 'CS-2024', 'RFID001', 'active'),
('Jane Smith', 'STU002', '<EMAIL>', 'Information Technology', 'IT-2024', 'RFID002', 'active'),
('Mike Johnson', 'STU003', '<EMAIL>', 'Software Engineering', 'SE-2024', 'RFID003', 'active'),
('Sarah Wilson', 'STU004', '<EMAIL>', 'Computer Science', 'CS-2024', 'RFID004', 'active'),
('David Brown', 'STU005', '<EMAIL>', 'Information Technology', 'IT-2024', 'RFID005', 'active');

-- Insert some sample attendance data
INSERT IGNORE INTO `attendance` (`student_id`, `date`, `time_in`, `status`, `marked_by`) VALUES
(1, CURDATE(), '08:30:00', 'present', 1),
(2, CURDATE(), '08:45:00', 'present', 1),
(3, CURDATE(), NULL, 'absent', 1),
(4, CURDATE(), '09:15:00', 'late', 1),
(5, CURDATE(), '08:30:00', 'partial', 1);

-- Insert some sample payments
INSERT IGNORE INTO `payments` (`student_id`, `amount`, `payment_type`, `payment_date`, `description`, `created_by`) VALUES
(1, 500.00, 'tuition', CURDATE(), 'Semester fee payment', 1),
(2, 300.00, 'fees', CURDATE(), 'Lab fee payment', 1),
(4, 150.00, 'books', DATE_SUB(CURDATE(), INTERVAL 5 DAY), 'Textbook purchase', 1);

-- Insert some sample exams
INSERT IGNORE INTO `exams` (`title`, `description`, `exam_date`, `duration`, `total_marks`, `created_by`) VALUES
('Database Systems Midterm', 'Midterm examination for Database Systems course', DATE_ADD(CURDATE(), INTERVAL 7 DAY), 120, 100, 1),
('Programming Final', 'Final examination for Programming course', DATE_ADD(CURDATE(), INTERVAL 14 DAY), 180, 150, 1),
('Web Development Quiz', 'Weekly quiz for Web Development', DATE_ADD(CURDATE(), INTERVAL 2 DAY), 60, 50, 1);

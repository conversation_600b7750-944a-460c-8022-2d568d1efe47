<?php
$pageTitle = 'Color Scheme Demo';
require_once __DIR__ . '/../includes/header.php';
?>

<!-- Color Scheme Demonstration -->
<div class="row mb-4">
    <div class="col-12">
        <div class="table-card">
            <div class="table-header">
                <h5 class="mb-0">Three Main Colors Theme</h5>
                <p class="text-muted mb-0">Professional Blue • Success Green • Warning Orange</p>
            </div>
            <div class="p-4">
                <div class="row">
                    <!-- Primary Color -->
                    <div class="col-md-4 mb-4">
                        <div class="text-center">
                            <div class="color-swatch" style="background: var(--primary-color); height: 100px; border-radius: 12px; margin-bottom: 1rem;"></div>
                            <h6 class="fw-bold">Primary Color</h6>
                            <p class="text-muted small">#2563eb - Professional Blue</p>
                            <div class="d-flex justify-content-center gap-2">
                                <div class="color-variant" style="background: var(--primary-light); width: 30px; height: 30px; border-radius: 6px;" title="Light"></div>
                                <div class="color-variant" style="background: var(--primary-color); width: 30px; height: 30px; border-radius: 6px;" title="Main"></div>
                                <div class="color-variant" style="background: var(--primary-dark); width: 30px; height: 30px; border-radius: 6px;" title="Dark"></div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Secondary Color -->
                    <div class="col-md-4 mb-4">
                        <div class="text-center">
                            <div class="color-swatch" style="background: var(--secondary-color); height: 100px; border-radius: 12px; margin-bottom: 1rem;"></div>
                            <h6 class="fw-bold">Secondary Color</h6>
                            <p class="text-muted small">#10b981 - Success Green</p>
                            <div class="d-flex justify-content-center gap-2">
                                <div class="color-variant" style="background: var(--secondary-light); width: 30px; height: 30px; border-radius: 6px;" title="Light"></div>
                                <div class="color-variant" style="background: var(--secondary-color); width: 30px; height: 30px; border-radius: 6px;" title="Main"></div>
                                <div class="color-variant" style="background: var(--secondary-dark); width: 30px; height: 30px; border-radius: 6px;" title="Dark"></div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Accent Color -->
                    <div class="col-md-4 mb-4">
                        <div class="text-center">
                            <div class="color-swatch" style="background: var(--accent-color); height: 100px; border-radius: 12px; margin-bottom: 1rem;"></div>
                            <h6 class="fw-bold">Accent Color</h6>
                            <p class="text-muted small">#f59e0b - Warning Orange</p>
                            <div class="d-flex justify-content-center gap-2">
                                <div class="color-variant" style="background: var(--accent-light); width: 30px; height: 30px; border-radius: 6px;" title="Light"></div>
                                <div class="color-variant" style="background: var(--accent-color); width: 30px; height: 30px; border-radius: 6px;" title="Main"></div>
                                <div class="color-variant" style="background: var(--accent-dark); width: 30px; height: 30px; border-radius: 6px;" title="Dark"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Button Examples -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="table-card">
            <div class="table-header">
                <h5 class="mb-0">Button Styles - Specific Usage</h5>
            </div>
            <div class="p-4">
                <div class="d-flex flex-wrap gap-3 mb-3">
                    <button class="btn btn-add">
                        <i class="fas fa-plus me-2"></i>Add Button (Blue)
                    </button>
                    <button class="btn btn-fresh">
                        <i class="fas fa-sync me-2"></i>Fresh Button (Blue)
                    </button>
                    <button class="btn btn-success">
                        <i class="fas fa-check me-2"></i>Success (Green)
                    </button>
                    <button class="btn btn-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>Warning (Orange)
                    </button>
                </div>
                <div class="d-flex flex-wrap gap-3">
                    <button class="btn btn-delete">
                        <i class="fas fa-trash me-2"></i>Delete (Red)
                    </button>
                    <button class="btn btn-clear">
                        <i class="fas fa-times me-2"></i>Clear (Red)
                    </button>
                    <button class="btn btn-error">
                        <i class="fas fa-exclamation-circle me-2"></i>Error (Red)
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="table-card">
            <div class="table-header">
                <h5 class="mb-0">Table Example with Grey-Black Header</h5>
            </div>
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>Student Name</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>John Doe</td>
                            <td><span class="badge" style="background: rgba(16, 185, 129, 0.1); color: var(--secondary-color);">Active</span></td>
                            <td>
                                <button class="btn btn-sm btn-add me-1"><i class="fas fa-plus"></i></button>
                                <button class="btn btn-sm btn-delete"><i class="fas fa-trash"></i></button>
                            </td>
                        </tr>
                        <tr>
                            <td>Jane Smith</td>
                            <td><span class="badge" style="background: rgba(245, 158, 11, 0.1); color: var(--accent-color);">Pending</span></td>
                            <td>
                                <button class="btn btn-sm btn-fresh me-1"><i class="fas fa-sync"></i></button>
                                <button class="btn btn-sm btn-clear"><i class="fas fa-times"></i></button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards with Three Colors -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="stats-card">
            <div class="d-flex align-items-center">
                <div class="stats-icon" style="background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);">
                    <i class="fas fa-users"></i>
                </div>
                <div class="ms-3">
                    <div class="text-muted small">Total Students</div>
                    <div class="h4 mb-0">1,234</div>
                    <div class="small" style="color: var(--primary-color);">Primary themed</div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="stats-card">
            <div class="d-flex align-items-center">
                <div class="stats-icon" style="background: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-light) 100%);">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="ms-3">
                    <div class="text-muted small">Present Today</div>
                    <div class="h4 mb-0">987</div>
                    <div class="small" style="color: var(--secondary-color);">Secondary themed</div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="stats-card">
            <div class="d-flex align-items-center">
                <div class="stats-icon" style="background: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-light) 100%);">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="ms-3">
                    <div class="text-muted small">Pending Items</div>
                    <div class="h4 mb-0">45</div>
                    <div class="small" style="color: var(--accent-color);">Accent themed</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Form Elements -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="table-card">
            <div class="table-header">
                <h5 class="mb-0">Form Elements</h5>
            </div>
            <div class="p-4">
                <form>
                    <div class="mb-3">
                        <label class="form-label">Primary Input</label>
                        <input type="text" class="form-control" placeholder="Focus to see primary color">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Select Dropdown</label>
                        <select class="form-select">
                            <option>Choose an option</option>
                            <option>Primary Option</option>
                            <option>Secondary Option</option>
                            <option>Accent Option</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="checkbox1">
                            <label class="form-check-label" for="checkbox1">
                                Primary checkbox
                            </label>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="table-card">
            <div class="table-header">
                <h5 class="mb-0">Progress Indicators</h5>
            </div>
            <div class="p-4">
                <div class="mb-3">
                    <label class="form-label">Primary Progress</label>
                    <div class="progress" style="height: 8px;">
                        <div class="progress-bar" style="width: 75%; background: var(--primary-color);" role="progressbar"></div>
                    </div>
                </div>
                <div class="mb-3">
                    <label class="form-label">Success Progress</label>
                    <div class="progress" style="height: 8px;">
                        <div class="progress-bar" style="width: 60%; background: var(--secondary-color);" role="progressbar"></div>
                    </div>
                </div>
                <div class="mb-3">
                    <label class="form-label">Warning Progress</label>
                    <div class="progress" style="height: 8px;">
                        <div class="progress-bar" style="width: 40%; background: var(--accent-color);" role="progressbar"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Gradient Examples -->
<div class="row mb-4">
    <div class="col-12">
        <div class="table-card">
            <div class="table-header">
                <h5 class="mb-0">Gradient Combinations</h5>
            </div>
            <div class="p-4">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <div class="text-center">
                            <div style="background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%); height: 80px; border-radius: 12px; margin-bottom: 0.5rem;"></div>
                            <small class="text-muted">Primary Gradient</small>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="text-center">
                            <div style="background: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-light) 100%); height: 80px; border-radius: 12px; margin-bottom: 0.5rem;"></div>
                            <small class="text-muted">Secondary Gradient</small>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="text-center">
                            <div style="background: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-light) 100%); height: 80px; border-radius: 12px; margin-bottom: 0.5rem;"></div>
                            <small class="text-muted">Accent Gradient</small>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="text-center">
                            <div style="background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 50%, var(--accent-color) 100%); height: 80px; border-radius: 12px; margin-bottom: 0.5rem;"></div>
                            <small class="text-muted">Mixed Gradient</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Color Usage Guidelines -->
<div class="row">
    <div class="col-12">
        <div class="table-card">
            <div class="table-header">
                <h5 class="mb-0">Updated Color Usage Guidelines</h5>
            </div>
            <div class="p-4">
                <div class="row">
                    <div class="col-md-3">
                        <h6 style="color: var(--primary-color);">
                            <i class="fas fa-palette me-2"></i>Blue - Add/Fresh Actions
                        </h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>Add buttons (.btn-add)</li>
                            <li><i class="fas fa-check text-success me-2"></i>Fresh/Refresh (.btn-fresh)</li>
                            <li><i class="fas fa-check text-success me-2"></i>Search and filter actions</li>
                            <li><i class="fas fa-check text-success me-2"></i>Primary navigation</li>
                        </ul>
                    </div>
                    <div class="col-md-3">
                        <h6 style="color: var(--secondary-color);">
                            <i class="fas fa-leaf me-2"></i>Green - Success States
                        </h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>Success messages</li>
                            <li><i class="fas fa-check text-success me-2"></i>Positive actions</li>
                            <li><i class="fas fa-check text-success me-2"></i>Attendance present</li>
                            <li><i class="fas fa-check text-success me-2"></i>Payment completed</li>
                        </ul>
                    </div>
                    <div class="col-md-3">
                        <h6 style="color: var(--accent-color);">
                            <i class="fas fa-sun me-2"></i>Orange - Warning States
                        </h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>Warning messages</li>
                            <li><i class="fas fa-check text-success me-2"></i>Pending states</li>
                            <li><i class="fas fa-check text-success me-2"></i>Late attendance</li>
                            <li><i class="fas fa-check text-success me-2"></i>Due payments</li>
                        </ul>
                    </div>
                    <div class="col-md-3">
                        <h6 style="color: #dc2626;">
                            <i class="fas fa-exclamation-triangle me-2"></i>Red - Delete/Error Actions
                        </h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>Delete buttons (.btn-delete)</li>
                            <li><i class="fas fa-check text-success me-2"></i>Clear actions (.btn-clear)</li>
                            <li><i class="fas fa-check text-success me-2"></i>Error messages (.btn-error)</li>
                            <li><i class="fas fa-check text-success me-2"></i>Critical alerts</li>
                        </ul>
                    </div>
                </div>

                <hr class="my-4">

                <div class="row">
                    <div class="col-md-6">
                        <h6 style="color: #374151;">
                            <i class="fas fa-table me-2"></i>Grey-Black Table Headers
                        </h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>All table headers use grey-black (#374151)</li>
                            <li><i class="fas fa-check text-success me-2"></i>Card headers also use grey-black background</li>
                            <li><i class="fas fa-check text-success me-2"></i>White text on dark background for contrast</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 style="color: #000000;">
                            <i class="fas fa-eye-slash me-2"></i>No Background Colors
                        </h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>Clean white background for main content</li>
                            <li><i class="fas fa-check text-success me-2"></i>Removed gradient backgrounds</li>
                            <li><i class="fas fa-check text-success me-2"></i>Focus on content, not decorative backgrounds</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/../includes/footer.php'; ?>

<?php
$pageTitle = 'Attendance Management';
require_once __DIR__ . '/../includes/header.php';

// Handle date selection
$selectedDate = $_GET['date'] ?? date('Y-m-d');
$selectedClass = $_GET['class'] ?? '';

// Handle attendance submission
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['attendance'])) {
    try {
        $db = getDB();
        $db->beginTransaction();
        
        foreach ($_POST['attendance'] as $studentId => $status) {
            // Check if attendance already exists for this date
            $checkStmt = $db->prepare("SELECT id FROM attendance WHERE student_id = ? AND date = ?");
            $checkStmt->execute([$studentId, $selectedDate]);
            
            if ($checkStmt->fetch()) {
                // Update existing record
                $updateStmt = $db->prepare("
                    UPDATE attendance 
                    SET status = ?, time_in = ?, marked_by = ?, updated_at = NOW() 
                    WHERE student_id = ? AND date = ?
                ");
                $timeIn = ($status == 'present' || $status == 'late') ? date('H:i:s') : null;
                $updateStmt->execute([$status, $timeIn, $_SESSION['user_id'], $studentId, $selectedDate]);
            } else {
                // Insert new record
                $insertStmt = $db->prepare("
                    INSERT INTO attendance (student_id, date, status, time_in, marked_by) 
                    VALUES (?, ?, ?, ?, ?)
                ");
                $timeIn = ($status == 'present' || $status == 'late') ? date('H:i:s') : null;
                $insertStmt->execute([$studentId, $selectedDate, $status, $timeIn, $_SESSION['user_id']]);
            }
        }
        
        $db->commit();
        setFlashMessage('success', 'Attendance marked successfully!');
        header('Location: ' . $_SERVER['REQUEST_URI']);
        exit();
        
    } catch (Exception $e) {
        $db->rollBack();
        error_log("Attendance error: " . $e->getMessage());
        setFlashMessage('danger', 'Error marking attendance. Please try again.');
    }
}

try {
    $db = getDB();
    
    // Get available classes
    $classStmt = $db->query("SELECT DISTINCT class FROM students WHERE status = 'active' ORDER BY class");
    $classes = $classStmt->fetchAll();
    
    // Build query for students
    $whereClause = "WHERE s.status = 'active'";
    $params = [];
    
    if (!empty($selectedClass)) {
        $whereClause .= " AND s.class = ?";
        $params[] = $selectedClass;
    }
    
    // Get students with their attendance for the selected date
    $stmt = $db->prepare("
        SELECT s.*, a.status as attendance_status, a.time_in, a.time_out
        FROM students s
        LEFT JOIN attendance a ON s.id = a.student_id AND a.date = ?
        $whereClause
        ORDER BY s.name
    ");
    $stmt->execute(array_merge([$selectedDate], $params));
    $students = $stmt->fetchAll();
    
    // Get attendance statistics for the selected date
    $statsStmt = $db->prepare("
        SELECT
            COUNT(DISTINCT s.id) as total_students,
            COUNT(CASE WHEN a.status = 'present' THEN 1 END) as present,
            COUNT(CASE WHEN a.status = 'absent' THEN 1 END) as absent,
            COUNT(CASE WHEN a.status = 'late' THEN 1 END) as late,
            COUNT(CASE WHEN a.status = 'excused' THEN 1 END) as excused,
            COUNT(CASE WHEN a.status = 'partial' THEN 1 END) as partial
        FROM students s
        LEFT JOIN attendance a ON s.id = a.student_id AND a.date = ?
        $whereClause
    ");
    $statsStmt->execute(array_merge([$selectedDate], $params));
    $stats = $statsStmt->fetch();
    
} catch (Exception $e) {
    error_log("Attendance page error: " . $e->getMessage());
    $students = [];
    $classes = [];
    $stats = ['total_students' => 0, 'present' => 0, 'absent' => 0, 'late' => 0, 'excused' => 0, 'partial' => 0];
}

$attendanceRate = $stats['total_students'] > 0 ? round(($stats['present'] / $stats['total_students']) * 100, 1) : 0;
?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="page-title mb-0">Attendance Management</h1>
        <p class="text-muted">Mark and manage student attendance</p>
    </div>
    <div>
        <a href="<?php echo BASE_URL; ?>attendance/reports.php" class="btn btn-outline-primary me-2">
            <i class="fas fa-chart-bar me-2"></i>Reports
        </a>
        <a href="<?php echo BASE_URL; ?>attendance/bulk.php" class="btn btn-add">
            <i class="fas fa-users me-2"></i>Bulk Mark
        </a>
    </div>
</div>

<!-- Filters -->
<div class="table-card mb-4">
    <div class="table-header">
        <form method="GET" class="row align-items-end">
            <div class="col-md-4">
                <label for="date" class="form-label">Date</label>
                <input type="date" class="form-control" id="date" name="date" value="<?php echo $selectedDate; ?>">
            </div>
            <div class="col-md-4">
                <label for="class" class="form-label">Class</label>
                <select class="form-select" id="class" name="class">
                    <option value="">All Classes</option>
                    <?php foreach ($classes as $class): ?>
                        <option value="<?php echo htmlspecialchars($class['class']); ?>" 
                                <?php echo $selectedClass == $class['class'] ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($class['class']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-4">
                <button type="submit" class="btn btn-fresh">
                    <i class="fas fa-filter me-2"></i>Filter
                </button>
                <a href="<?php echo BASE_URL; ?>attendance/index.php" class="btn btn-clear ms-2">
                    <i class="fas fa-times me-2"></i>Clear
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Statistics - One Row Layout -->
<div class="row mb-4">
    <div class="col-12">
        <div class="table-card">
            <div class="table-header">
                <h5 class="mb-0">Attendance Statistics - <?php echo date('F j, Y', strtotime($selectedDate)); ?></h5>
            </div>
            <div class="p-3">
                <div class="row g-2">
                    <!-- Total Students -->
                    <div class="col-md-2 col-6">
                        <div class="attendance-stat-item text-center">
                            <div class="stats-icon mx-auto mb-2" style="background: linear-gradient(135deg, var(--color-primary) 0%, var(--primary-light) 100%); width: 40px; height: 40px;">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="fw-bold h5 mb-1"><?php echo $stats['total_students']; ?></div>
                            <div class="small text-muted">Total Students</div>
                        </div>
                    </div>

                    <!-- Present Today -->
                    <div class="col-md-2 col-6">
                        <div class="attendance-stat-item text-center">
                            <div class="stats-icon mx-auto mb-2" style="background: linear-gradient(135deg, var(--color-secondary) 0%, var(--secondary-light) 100%); width: 40px; height: 40px;">
                                <i class="fas fa-user-check"></i>
                            </div>
                            <div class="fw-bold h5 mb-1"><?php echo $stats['present']; ?></div>
                            <div class="small text-muted">Present Today</div>
                            <div class="small" style="color: var(--color-secondary);"><?php echo $attendanceRate; ?>%</div>
                        </div>
                    </div>

                    <!-- Absent -->
                    <div class="col-md-2 col-6">
                        <div class="attendance-stat-item text-center">
                            <div class="stats-icon mx-auto mb-2" style="background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); width: 40px; height: 40px;">
                                <i class="fas fa-user-times"></i>
                            </div>
                            <div class="fw-bold h5 mb-1"><?php echo $stats['absent']; ?></div>
                            <div class="small text-muted">Absent</div>
                        </div>
                    </div>

                    <!-- Partial -->
                    <div class="col-md-2 col-6">
                        <div class="attendance-stat-item text-center">
                            <div class="stats-icon mx-auto mb-2" style="background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%); width: 40px; height: 40px;">
                                <i class="fas fa-user-clock"></i>
                            </div>
                            <div class="fw-bold h5 mb-1"><?php echo $stats['partial']; ?></div>
                            <div class="small text-muted">Partial</div>
                        </div>
                    </div>

                    <!-- Late -->
                    <div class="col-md-2 col-6">
                        <div class="attendance-stat-item text-center">
                            <div class="stats-icon mx-auto mb-2" style="background: linear-gradient(135deg, var(--color-accent) 0%, var(--accent-light) 100%); width: 40px; height: 40px;">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="fw-bold h5 mb-1"><?php echo $stats['late']; ?></div>
                            <div class="small text-muted">Late</div>
                        </div>
                    </div>

                    <!-- Excused -->
                    <div class="col-md-2 col-6">
                        <div class="attendance-stat-item text-center">
                            <div class="stats-icon mx-auto mb-2" style="background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%); width: 40px; height: 40px;">
                                <i class="fas fa-user-shield"></i>
                            </div>
                            <div class="fw-bold h5 mb-1"><?php echo $stats['excused']; ?></div>
                            <div class="small text-muted">Excused</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Attendance Form -->
<div class="table-card">
    <div class="table-header">
        <h5 class="mb-0">Mark Attendance - <?php echo date('F j, Y', strtotime($selectedDate)); ?></h5>
    </div>
    
    <form method="POST">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>Student</th>
                        <th>Class</th>
                        <th>Status</th>
                        <th>Time In</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($students)): ?>
                        <tr>
                            <td colspan="5" class="text-center text-muted py-4">
                                No students found for the selected criteria
                            </td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($students as $student): ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-circle me-3">
                                            <?php echo strtoupper(substr($student['name'], 0, 2)); ?>
                                        </div>
                                        <div>
                                            <div class="fw-medium"><?php echo htmlspecialchars($student['name']); ?></div>
                                            <div class="small text-muted"><?php echo htmlspecialchars($student['student_id']); ?></div>
                                        </div>
                                    </div>
                                </td>
                                <td><?php echo htmlspecialchars($student['class']); ?></td>
                                <td>
                                    <select name="attendance[<?php echo $student['id']; ?>]" class="form-select form-select-sm">
                                        <option value="">Not Marked</option>
                                        <option value="present" <?php echo $student['attendance_status'] == 'present' ? 'selected' : ''; ?>>Present</option>
                                        <option value="absent" <?php echo $student['attendance_status'] == 'absent' ? 'selected' : ''; ?>>Absent</option>
                                        <option value="partial" <?php echo $student['attendance_status'] == 'partial' ? 'selected' : ''; ?>>Partial</option>
                                        <option value="late" <?php echo $student['attendance_status'] == 'late' ? 'selected' : ''; ?>>Late</option>
                                        <option value="excused" <?php echo $student['attendance_status'] == 'excused' ? 'selected' : ''; ?>>Excused</option>
                                    </select>
                                </td>
                                <td>
                                    <?php if ($student['time_in']): ?>
                                        <span class="text-success"><?php echo date('H:i', strtotime($student['time_in'])); ?></span>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button type="button" class="btn btn-outline-success btn-sm"
                                                onclick="markStatus(<?php echo $student['id']; ?>, 'present')"
                                                title="Mark Present">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-warning btn-sm"
                                                onclick="markStatus(<?php echo $student['id']; ?>, 'partial')"
                                                title="Mark Partial">
                                            <i class="fas fa-user-clock"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-danger btn-sm"
                                                onclick="markStatus(<?php echo $student['id']; ?>, 'absent')"
                                                title="Mark Absent">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
        
        <?php if (!empty($students)): ?>
            <div class="p-3 border-top">
                <button type="submit" class="btn btn-add">
                    <i class="fas fa-save me-2"></i>Save Attendance
                </button>
                <button type="button" class="btn btn-outline-success ms-2" onclick="markAllPresent()">
                    <i class="fas fa-check-double me-2"></i>Mark All Present
                </button>
            </div>
        <?php endif; ?>
    </form>
</div>

<style>
.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.875rem;
}

/* Professional One-Row Stats Layout */
.attendance-stats-row {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
}

.attendance-stats-row .stats-icon {
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    color: white;
    transition: transform 0.2s ease;
}

.attendance-stats-row .stats-icon:hover {
    transform: scale(1.1);
}

.attendance-stat-item {
    padding: 0.75rem;
    border-radius: 8px;
    transition: all 0.2s ease;
    border: 1px solid transparent;
}

.attendance-stat-item:hover {
    background: rgba(0, 0, 0, 0.02);
    border-color: #e5e7eb;
    transform: translateY(-1px);
}

@media (max-width: 768px) {
    .attendance-stats-row .row > div {
        margin-bottom: 1rem;
    }
}

/* Additional button styles for attendance actions */
.btn-outline-warning {
    color: var(--color-accent);
    border-color: var(--color-accent);
    background: transparent;
}

.btn-outline-warning:hover {
    background: var(--color-accent);
    border-color: var(--color-accent);
    color: white;
}
</style>

<script>
function markStatus(studentId, status) {
    const select = document.querySelector(`select[name="attendance[${studentId}]"]`);
    if (select) {
        select.value = status;
    }
}

function markAllPresent() {
    const selects = document.querySelectorAll('select[name^="attendance["]');
    selects.forEach(select => {
        select.value = 'present';
    });
}
</script>

<?php require_once __DIR__ . '/../includes/footer.php'; ?>

-- Advanced Attendance System Database Schema
-- Run this script to create all necessary tables

CREATE DATABASE IF NOT EXISTS attendance_system;
USE attendance_system;

-- Users table for authentication
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHA<PERSON>(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100),
    role ENUM('admin', 'teacher', 'staff') DEFAULT 'teacher',
    status ENUM('active', 'inactive') DEFAULT 'active',
    last_login DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Students table
CREATE TABLE IF NOT EXISTS students (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_id VARCHAR(20) UNIQUE NOT NULL,
    name <PERSON><PERSON>HA<PERSON>(100) NOT NULL,
    email VARCHAR(100),
    phone VARCHAR(20),
    address TEXT,
    date_of_birth DATE,
    gender ENUM('male', 'female', 'other'),
    guardian_name VA<PERSON>HA<PERSON>(100),
    guardian_phone VARCHAR(20),
    class VARCHAR(50),
    section VARCHAR(10),
    admission_date DATE,
    status ENUM('active', 'inactive', 'graduated') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Attendance table
CREATE TABLE IF NOT EXISTS attendance (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT NOT NULL,
    date DATE NOT NULL,
    status ENUM('present', 'absent', 'late', 'excused') NOT NULL,
    time_in TIME,
    time_out TIME,
    notes TEXT,
    marked_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (marked_by) REFERENCES users(id),
    UNIQUE KEY unique_student_date (student_id, date)
);

-- Payments table
CREATE TABLE IF NOT EXISTS payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    payment_date DATE NOT NULL,
    payment_type ENUM('tuition', 'fees', 'books', 'transport', 'other') DEFAULT 'tuition',
    payment_method ENUM('cash', 'card', 'bank_transfer', 'cheque') DEFAULT 'cash',
    reference_number VARCHAR(50),
    notes TEXT,
    status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'completed',
    recorded_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (recorded_by) REFERENCES users(id)
);

-- Classes table
CREATE TABLE IF NOT EXISTS classes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    description TEXT,
    teacher_id INT,
    capacity INT DEFAULT 30,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (teacher_id) REFERENCES users(id)
);

-- Subjects table
CREATE TABLE IF NOT EXISTS subjects (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(20) UNIQUE,
    description TEXT,
    credits INT DEFAULT 1,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Settings table
CREATE TABLE IF NOT EXISTS settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    description TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert default admin user (password: admin123)
INSERT IGNORE INTO users (username, password, email, role, status) VALUES 
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', 'admin', 'active'),
('teacher', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', 'teacher', 'active');

-- Insert sample students
INSERT IGNORE INTO students (student_id, name, email, phone, class, section, admission_date, status) VALUES 
('STU001', 'John Doe', '<EMAIL>', '+1234567890', 'Grade 10', 'A', '2024-01-15', 'active'),
('STU002', 'Jane Smith', '<EMAIL>', '+1234567891', 'Grade 10', 'A', '2024-01-15', 'active'),
('STU003', 'Mike Johnson', '<EMAIL>', '+1234567892', 'Grade 11', 'B', '2024-01-16', 'active'),
('STU004', 'Sarah Wilson', '<EMAIL>', '+1234567893', 'Grade 11', 'B', '2024-01-16', 'active'),
('STU005', 'David Brown', '<EMAIL>', '+1234567894', 'Grade 12', 'A', '2024-01-17', 'active');

-- Insert sample attendance records
INSERT IGNORE INTO attendance (student_id, date, status, time_in, marked_by) VALUES 
(1, CURDATE(), 'present', '08:30:00', 1),
(2, CURDATE(), 'present', '08:25:00', 1),
(3, CURDATE(), 'late', '08:45:00', 1),
(4, CURDATE(), 'absent', NULL, 1),
(5, CURDATE(), 'present', '08:20:00', 1);

-- Insert sample payments
INSERT IGNORE INTO payments (student_id, amount, payment_date, payment_type, payment_method, recorded_by) VALUES 
(1, 500.00, CURDATE(), 'tuition', 'cash', 1),
(2, 500.00, CURDATE(), 'tuition', 'card', 1),
(3, 450.00, DATE_SUB(CURDATE(), INTERVAL 1 DAY), 'tuition', 'bank_transfer', 1),
(5, 500.00, DATE_SUB(CURDATE(), INTERVAL 2 DAY), 'tuition', 'cash', 1);

-- Insert default settings
INSERT IGNORE INTO settings (setting_key, setting_value, description) VALUES 
('school_name', 'Advanced Learning Academy', 'Name of the school'),
('school_address', '123 Education Street, Learning City', 'School address'),
('school_phone', '******-0123', 'School contact phone'),
('school_email', '<EMAIL>', 'School contact email'),
('academic_year', '2024-2025', 'Current academic year'),
('currency', 'USD', 'Currency for payments'),
('timezone', 'America/New_York', 'School timezone');

-- Create indexes for better performance
CREATE INDEX idx_attendance_date ON attendance(date);
CREATE INDEX idx_attendance_student ON attendance(student_id);
CREATE INDEX idx_payments_date ON payments(payment_date);
CREATE INDEX idx_payments_student ON payments(student_id);
CREATE INDEX idx_students_status ON students(status);
CREATE INDEX idx_students_class ON students(class);

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Settings - Advanced Attendance System</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/shared.css" rel="stylesheet">
</head>
<body>
    <div class="layout-container">
        <!-- Navigation will be loaded here -->
        <div id="navigation-placeholder"></div>

        <!-- Main Content Area -->
        <main class="main-content" id="main-content">
            <!-- Mobile Header -->
            <div class="mobile-header">
                <div class="mobile-header-content">
                    <button class="mobile-toggle" onclick="toggleSidebar()">
                        <i class="fas fa-bars"></i>
                    </button>
                    <div class="logo">
                        <div class="logo-icon">
                            <i class="fas fa-fingerprint"></i>
                        </div>
                        Attendance System
                    </div>
                </div>
            </div>

            <!-- Page Header -->
            <div class="page-header">
                <h1 class="page-title">
                    <i class="fas fa-cogs"></i>
                    System Settings
                </h1>
                <p class="page-subtitle">Configure and monitor your attendance management system</p>
            </div>

            <!-- System Status Overview -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">System Uptime</div>
                        <div class="stat-icon present">
                            <i class="fas fa-clock"></i>
                        </div>
                    </div>
                    <div class="stat-value" id="system-uptime">0h 0m</div>
                    <div class="stat-change">Since last restart</div>
                </div>
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">Database Status</div>
                        <div class="stat-icon present">
                            <i class="fas fa-database"></i>
                        </div>
                    </div>
                    <div class="stat-value" id="database-status">Connected</div>
                    <div class="stat-change">Database connection</div>
                </div>
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">ESP32 Status</div>
                        <div class="stat-icon" id="esp32-status-icon">
                            <i class="fas fa-microchip"></i>
                        </div>
                    </div>
                    <div class="stat-value" id="esp32-connection-status">Checking...</div>
                    <div class="stat-change">Hardware connection</div>
                </div>
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">Total Records</div>
                        <div class="stat-icon total">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                    </div>
                    <div class="stat-value" id="total-records">0</div>
                    <div class="stat-change">All system records</div>
                </div>
            </div>

            <!-- Hardware Configuration -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <div class="card-icon" style="background: var(--gradient-primary);">
                            <i class="fas fa-microchip"></i>
                        </div>
                        Hardware Configuration
                    </h3>
                </div>
                <div class="hardware-status">
                    <div class="hardware-card waiting" id="system-rfid-card">
                        <div class="hardware-icon rfid">
                            <i class="fas fa-credit-card"></i>
                        </div>
                        <div class="hardware-title">RFID Scanner</div>
                        <div class="hardware-status-text waiting" id="system-rfid-status">Checking...</div>
                        <div class="hardware-details">
                            <div>Port: COM3</div>
                            <div>Baud Rate: 9600</div>
                        </div>
                        <div style="margin-top: 1rem;">
                            <button onclick="testRFID()" class="btn btn-primary" style="width: 100%; padding: 0.5rem;">
                                <i class="fas fa-play"></i>
                                Test RFID
                            </button>
                        </div>
                    </div>
                    
                    <div class="hardware-card waiting" id="system-fingerprint-card">
                        <div class="hardware-icon fingerprint">
                            <i class="fas fa-fingerprint"></i>
                        </div>
                        <div class="hardware-title">Fingerprint Scanner</div>
                        <div class="hardware-status-text waiting" id="system-fingerprint-status">Checking...</div>
                        <div class="hardware-details">
                            <div>Model: R307</div>
                            <div>Capacity: 1000</div>
                        </div>
                        <div style="margin-top: 1rem;">
                            <button onclick="testFingerprint()" class="btn btn-primary" style="width: 100%; padding: 0.5rem;">
                                <i class="fas fa-play"></i>
                                Test Fingerprint
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- System Configuration -->
            <div class="form-grid">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <div class="card-icon" style="background: var(--gradient-success);">
                                <i class="fas fa-cog"></i>
                            </div>
                            General Settings
                        </h3>
                    </div>
                    <form id="system-settings-form">
                        <div class="form-group">
                            <label class="form-label" for="system-name">
                                <i class="fas fa-tag"></i>
                                System Name
                            </label>
                            <input type="text" id="system-name" class="form-input" value="Advanced Attendance System">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label" for="attendance-threshold">
                                <i class="fas fa-percentage"></i>
                                Attendance Threshold (%)
                            </label>
                            <input type="number" id="attendance-threshold" class="form-input" value="75" min="0" max="100">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label" for="auto-backup">
                                <i class="fas fa-save"></i>
                                Auto Backup
                            </label>
                            <select id="auto-backup" class="form-select">
                                <option value="daily">Daily</option>
                                <option value="weekly">Weekly</option>
                                <option value="monthly">Monthly</option>
                                <option value="disabled">Disabled</option>
                            </select>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            Save Settings
                        </button>
                    </form>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <div class="card-icon" style="background: var(--gradient-warning);">
                                <i class="fas fa-envelope"></i>
                            </div>
                            Email Configuration
                        </h3>
                    </div>
                    <form id="email-settings-form">
                        <div class="form-group">
                            <label class="form-label" for="smtp-server">
                                <i class="fas fa-server"></i>
                                SMTP Server
                            </label>
                            <input type="text" id="smtp-server" class="form-input" placeholder="smtp.gmail.com">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label" for="smtp-port">
                                <i class="fas fa-plug"></i>
                                SMTP Port
                            </label>
                            <input type="number" id="smtp-port" class="form-input" value="587">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label" for="email-username">
                                <i class="fas fa-user"></i>
                                Email Username
                            </label>
                            <input type="email" id="email-username" class="form-input" placeholder="<EMAIL>">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label" for="email-password">
                                <i class="fas fa-lock"></i>
                                Email Password
                            </label>
                            <input type="password" id="email-password" class="form-input" placeholder="App password">
                        </div>
                        
                        <div style="display: flex; gap: 0.5rem;">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i>
                                Save Email Settings
                            </button>
                            <button type="button" onclick="testEmail()" class="btn btn-success">
                                <i class="fas fa-paper-plane"></i>
                                Test Email
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- System Actions -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <div class="card-icon" style="background: var(--gradient-error);">
                            <i class="fas fa-tools"></i>
                        </div>
                        System Actions
                    </h3>
                </div>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                    <button onclick="backupDatabase()" class="btn btn-success">
                        <i class="fas fa-download"></i>
                        Backup Database
                    </button>
                    <button onclick="restoreDatabase()" class="btn btn-warning" style="background: linear-gradient(135deg, var(--warning) 0%, #d97706 100%);">
                        <i class="fas fa-upload"></i>
                        Restore Database
                    </button>
                    <button onclick="clearLogs()" class="btn btn-info" style="background: linear-gradient(135deg, var(--info) 0%, #0891b2 100%);">
                        <i class="fas fa-trash"></i>
                        Clear Logs
                    </button>
                    <button onclick="restartSystem()" class="btn btn-danger">
                        <i class="fas fa-power-off"></i>
                        Restart System
                    </button>
                </div>
            </div>

            <!-- System Logs -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <div class="card-icon" style="background: var(--gradient-primary);">
                            <i class="fas fa-list"></i>
                        </div>
                        System Logs
                    </h3>
                    <div style="display: flex; gap: 0.5rem;">
                        <select id="log-type" class="form-select" style="width: 150px;">
                            <option value="all">All Logs</option>
                            <option value="error">Error Logs</option>
                            <option value="security">Security Logs</option>
                            <option value="attendance">Attendance Logs</option>
                        </select>
                        <button onclick="refreshLogs()" class="btn btn-primary">
                            <i class="fas fa-refresh"></i>
                            Refresh
                        </button>
                    </div>
                </div>
                <div class="activity-feed" id="system-logs" style="max-height: 400px;">
                    <div class="activity-item">
                        <div class="activity-icon" style="background: var(--gradient-primary);">
                            <i class="fas fa-power-off"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-text">System initialized</div>
                            <div class="activity-time">Just now</div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="assets/js/shared.js"></script>
    <script>
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadNavigation();
            loadSystemData();
            initializeForms();
            
            // Refresh system status every 10 seconds
            setInterval(updateSystemStatus, 10000);
        });

        // Load navigation component
        async function loadNavigation() {
            try {
                const response = await fetch('components/navigation.html');
                const html = await response.text();
                document.getElementById('navigation-placeholder').innerHTML = html;
            } catch (error) {
                console.error('Error loading navigation:', error);
            }
        }

        // Load system data
        async function loadSystemData() {
            try {
                await Promise.all([
                    loadSystemStats(),
                    loadSystemSettings(),
                    updateSystemStatus(),
                    loadSystemLogs()
                ]);
            } catch (error) {
                console.error('Error loading system data:', error);
                AttendanceSystem.showNotification('Error loading system data', 'error');
            }
        }

        // Load system statistics
        async function loadSystemStats() {
            try {
                const response = await AttendanceSystem.apiRequest('api/system.php?action=stats');
                
                if (response.success) {
                    document.getElementById('system-uptime').textContent = response.uptime || '0h 0m';
                    document.getElementById('database-status').textContent = response.database_status || 'Unknown';
                    document.getElementById('total-records').textContent = response.total_records || 0;
                }
            } catch (error) {
                console.error('Error loading system stats:', error);
            }
        }

        // Load system settings
        async function loadSystemSettings() {
            try {
                const response = await AttendanceSystem.apiRequest('api/system.php?action=get_settings');
                
                if (response.success && response.settings) {
                    const settings = response.settings;
                    document.getElementById('system-name').value = settings.system_name || 'Advanced Attendance System';
                    document.getElementById('attendance-threshold').value = settings.attendance_threshold || 75;
                    document.getElementById('auto-backup').value = settings.auto_backup || 'daily';
                    
                    // Email settings
                    document.getElementById('smtp-server').value = settings.smtp_server || '';
                    document.getElementById('smtp-port').value = settings.smtp_port || 587;
                    document.getElementById('email-username').value = settings.email_username || '';
                    // Don't load password for security
                }
            } catch (error) {
                console.error('Error loading system settings:', error);
            }
        }

        // Update system status
        async function updateSystemStatus() {
            try {
                const response = await AttendanceSystem.apiRequest('api/system.php?action=hardware_status');
                
                if (response.success) {
                    // Update ESP32 status
                    const esp32Status = response.esp32_status || 'offline';
                    const esp32StatusElement = document.getElementById('esp32-connection-status');
                    const esp32IconElement = document.getElementById('esp32-status-icon');
                    
                    esp32StatusElement.textContent = esp32Status.charAt(0).toUpperCase() + esp32Status.slice(1);
                    esp32IconElement.className = `stat-icon ${esp32Status === 'online' ? 'present' : 'absent'}`;
                    
                    // Update hardware cards
                    updateHardwareCard('system-rfid-card', 'system-rfid-status', response.rfid_status || 'offline');
                    updateHardwareCard('system-fingerprint-card', 'system-fingerprint-status', response.fingerprint_status || 'offline');
                }
            } catch (error) {
                console.error('Error updating system status:', error);
            }
        }

        // Update hardware card status
        function updateHardwareCard(cardId, statusId, status) {
            const card = document.getElementById(cardId);
            const statusText = document.getElementById(statusId);
            
            if (card && statusText) {
                card.className = `hardware-card ${status}`;
                statusText.className = `hardware-status-text ${status}`;
                statusText.textContent = status.charAt(0).toUpperCase() + status.slice(1);
            }
        }

        // Load system logs
        async function loadSystemLogs() {
            try {
                const logType = document.getElementById('log-type').value;
                const response = await AttendanceSystem.apiRequest(`api/system.php?action=logs&type=${logType}`);
                
                if (response.success && response.logs) {
                    const logsContainer = document.getElementById('system-logs');
                    logsContainer.innerHTML = '';
                    
                    response.logs.forEach(log => {
                        const item = document.createElement('div');
                        item.className = 'activity-item';
                        item.innerHTML = `
                            <div class="activity-icon" style="background: ${getLogIconColor(log.type)};">
                                <i class="fas fa-${getLogIcon(log.type)}"></i>
                            </div>
                            <div class="activity-content">
                                <div class="activity-text">${log.message}</div>
                                <div class="activity-time">${AttendanceSystem.formatDateTime(log.timestamp)}</div>
                            </div>
                        `;
                        logsContainer.appendChild(item);
                    });
                }
            } catch (error) {
                console.error('Error loading system logs:', error);
            }
        }

        // Get log icon based on type
        function getLogIcon(type) {
            switch (type) {
                case 'error': return 'exclamation-triangle';
                case 'security': return 'shield-alt';
                case 'attendance': return 'calendar-check';
                default: return 'info-circle';
            }
        }

        // Get log icon color based on type
        function getLogIconColor(type) {
            switch (type) {
                case 'error': return 'var(--gradient-error)';
                case 'security': return 'var(--gradient-warning)';
                case 'attendance': return 'var(--gradient-success)';
                default: return 'var(--gradient-primary)';
            }
        }

        // Initialize forms
        function initializeForms() {
            document.getElementById('system-settings-form').addEventListener('submit', saveSystemSettings);
            document.getElementById('email-settings-form').addEventListener('submit', saveEmailSettings);
            document.getElementById('log-type').addEventListener('change', loadSystemLogs);
        }

        // Save system settings
        async function saveSystemSettings(e) {
            e.preventDefault();
            
            const settings = {
                system_name: document.getElementById('system-name').value,
                attendance_threshold: document.getElementById('attendance-threshold').value,
                auto_backup: document.getElementById('auto-backup').value
            };
            
            try {
                const response = await AttendanceSystem.apiRequest('api/system.php', {
                    method: 'POST',
                    body: JSON.stringify({
                        action: 'save_settings',
                        settings: settings
                    })
                });
                
                if (response.success) {
                    AttendanceSystem.showNotification('System settings saved successfully', 'success');
                } else {
                    AttendanceSystem.showNotification(response.message || 'Failed to save settings', 'error');
                }
            } catch (error) {
                console.error('Error saving system settings:', error);
                AttendanceSystem.showNotification('Failed to save settings', 'error');
            }
        }

        // Save email settings
        async function saveEmailSettings(e) {
            e.preventDefault();
            
            const emailSettings = {
                smtp_server: document.getElementById('smtp-server').value,
                smtp_port: document.getElementById('smtp-port').value,
                email_username: document.getElementById('email-username').value,
                email_password: document.getElementById('email-password').value
            };
            
            try {
                const response = await AttendanceSystem.apiRequest('api/system.php', {
                    method: 'POST',
                    body: JSON.stringify({
                        action: 'save_email_settings',
                        settings: emailSettings
                    })
                });
                
                if (response.success) {
                    AttendanceSystem.showNotification('Email settings saved successfully', 'success');
                    // Clear password field for security
                    document.getElementById('email-password').value = '';
                } else {
                    AttendanceSystem.showNotification(response.message || 'Failed to save email settings', 'error');
                }
            } catch (error) {
                console.error('Error saving email settings:', error);
                AttendanceSystem.showNotification('Failed to save email settings', 'error');
            }
        }

        // Test hardware functions
        async function testRFID() {
            AttendanceSystem.showNotification('Testing RFID scanner...', 'info');
            try {
                const response = await AttendanceSystem.apiRequest('api/rfid.php?action=test');
                if (response.success) {
                    AttendanceSystem.showNotification('RFID scanner test successful', 'success');
                } else {
                    AttendanceSystem.showNotification('RFID scanner test failed', 'error');
                }
            } catch (error) {
                AttendanceSystem.showNotification('RFID scanner test failed', 'error');
            }
        }

        async function testFingerprint() {
            AttendanceSystem.showNotification('Testing fingerprint scanner...', 'info');
            try {
                const response = await AttendanceSystem.apiRequest('api/fingerprint.php?action=test');
                if (response.success) {
                    AttendanceSystem.showNotification('Fingerprint scanner test successful', 'success');
                } else {
                    AttendanceSystem.showNotification('Fingerprint scanner test failed', 'error');
                }
            } catch (error) {
                AttendanceSystem.showNotification('Fingerprint scanner test failed', 'error');
            }
        }

        async function testEmail() {
            AttendanceSystem.showNotification('Sending test email...', 'info');
            try {
                const response = await AttendanceSystem.apiRequest('api/system.php', {
                    method: 'POST',
                    body: JSON.stringify({
                        action: 'test_email'
                    })
                });
                
                if (response.success) {
                    AttendanceSystem.showNotification('Test email sent successfully', 'success');
                } else {
                    AttendanceSystem.showNotification(response.message || 'Test email failed', 'error');
                }
            } catch (error) {
                AttendanceSystem.showNotification('Test email failed', 'error');
            }
        }

        // System action functions
        async function backupDatabase() {
            if (!confirm('Create a database backup? This may take a few moments.')) return;
            
            AttendanceSystem.showNotification('Creating database backup...', 'info');
            try {
                const response = await AttendanceSystem.apiRequest('api/system.php', {
                    method: 'POST',
                    body: JSON.stringify({
                        action: 'backup_database'
                    })
                });
                
                if (response.success) {
                    AttendanceSystem.showNotification('Database backup created successfully', 'success');
                    // Trigger download if backup file URL is provided
                    if (response.backup_url) {
                        const link = document.createElement('a');
                        link.href = response.backup_url;
                        link.download = response.filename || 'database_backup.sql';
                        link.click();
                    }
                } else {
                    AttendanceSystem.showNotification(response.message || 'Database backup failed', 'error');
                }
            } catch (error) {
                AttendanceSystem.showNotification('Database backup failed', 'error');
            }
        }

        function restoreDatabase() {
            AttendanceSystem.showNotification('Database restore feature coming soon', 'info');
        }

        async function clearLogs() {
            if (!confirm('Clear all system logs? This action cannot be undone.')) return;
            
            try {
                const response = await AttendanceSystem.apiRequest('api/system.php', {
                    method: 'POST',
                    body: JSON.stringify({
                        action: 'clear_logs'
                    })
                });
                
                if (response.success) {
                    AttendanceSystem.showNotification('System logs cleared', 'success');
                    loadSystemLogs();
                } else {
                    AttendanceSystem.showNotification(response.message || 'Failed to clear logs', 'error');
                }
            } catch (error) {
                AttendanceSystem.showNotification('Failed to clear logs', 'error');
            }
        }

        function restartSystem() {
            if (!confirm('Restart the system? This will temporarily interrupt all services.')) return;
            AttendanceSystem.showNotification('System restart feature coming soon', 'warning');
        }

        function refreshLogs() {
            loadSystemLogs();
            AttendanceSystem.showNotification('System logs refreshed', 'info');
        }

        // Mobile sidebar toggle
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            if (sidebar) {
                sidebar.classList.toggle('mobile-visible');
            }
        }
    </script>
</body>
</html>

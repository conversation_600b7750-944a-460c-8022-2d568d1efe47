<?php
require_once __DIR__ . '/../config/config.php';
requireLogin();
$currentUser = getCurrentUser();
$currentPage = basename($_SERVER['PHP_SELF'], '.php');
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($pageTitle) ? $pageTitle . ' - ' . APP_NAME : APP_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="<?php echo BASE_URL; ?>assets/css/admin.css" rel="stylesheet">
    
    <style>
        :root {
            /* Three Main Colors Theme */
            --primary-color: #2563eb;      /* Professional Blue */
            --secondary-color: #10b981;    /* Success Green */
            --accent-color: #f59e0b;       /* Warning Orange */

            /* Color Variations */
            --primary-light: #3b82f6;
            --primary-dark: #1d4ed8;
            --secondary-light: #34d399;
            --secondary-dark: #059669;
            --accent-light: #fbbf24;
            --accent-dark: #d97706;

            /* Semantic Colors */
            --danger-color: #ef4444;
            --info-color: var(--primary-color);
            --dark-color: #1f2937;
            --light-color: #f8fafc;
            --sidebar-width: 250px;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background-color: #ffffff;
            color: var(--dark-color);
        }

        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: var(--sidebar-width);
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 50%, var(--accent-color) 100%);
            color: white;
            z-index: 1000;
            overflow-y: auto;
        }

        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-brand {
            font-size: 1.25rem;
            font-weight: 700;
            color: white;
            text-decoration: none;
        }

        .sidebar-nav {
            padding: 1rem 0;
        }

        .nav-item {
            margin: 0.25rem 0;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 0.75rem 1.5rem;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .nav-link:hover,
        .nav-link.active {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .nav-link i {
            width: 20px;
            margin-right: 0.75rem;
        }

        .main-content {
            margin-left: var(--sidebar-width);
            min-height: 100vh;
        }

        .top-navbar {
            background: white;
            border-bottom: 1px solid #e5e7eb;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .content-wrapper {
            padding: 2rem;
        }

        .page-header {
            margin-bottom: 2rem;
        }

        .page-title {
            font-size: 1.875rem;
            font-weight: 700;
            color: var(--dark-color);
            margin: 0;
        }

        .stats-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
            transition: transform 0.2s ease;
        }

        .stats-card:hover {
            transform: translateY(-2px);
        }

        .stats-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }

        /* Button Colors - Specific Usage */
        .btn-primary, .btn-add, .btn-fresh {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }

        .btn-success {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
            color: white;
        }

        .btn-warning {
            background-color: var(--accent-color);
            border-color: var(--accent-color);
            color: white;
        }

        .btn-danger, .btn-delete, .btn-clear, .btn-error {
            background-color: #dc2626;
            border-color: #dc2626;
            color: white;
        }

        .btn-outline-primary {
            color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-outline-primary:hover {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }

        .btn-outline-danger {
            color: #dc2626;
            border-color: #dc2626;
        }

        .btn-outline-danger:hover {
            background-color: #dc2626;
            border-color: #dc2626;
            color: white;
        }

        .table-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
            overflow: hidden;
        }

        .table-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e5e7eb;
            background-color: #374151;
            color: white;
        }

        .table-header h5 {
            color: white;
            margin: 0;
        }

        .table thead th {
            background-color: #374151 !important;
            color: white !important;
            border: none;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-size: 0.875rem;
        }

        .badge {
            font-size: 0.75rem;
            padding: 0.375rem 0.75rem;
        }

        /* Alert and Error Styling */
        .alert-danger, .text-danger, .error-message {
            color: #dc2626 !important;
        }

        .alert-danger {
            background-color: rgba(220, 38, 38, 0.1);
            border-color: #dc2626;
        }

        /* Remove background colors from main content areas */
        .main-content {
            background: transparent;
        }

        .content-wrapper {
            background: transparent;
        }

        /* Table styling updates */
        .table tbody tr:hover {
            background-color: rgba(55, 65, 81, 0.05);
        }

        .table-light {
            background-color: #374151 !important;
            color: white !important;
        }

        .table-light th {
            background-color: #374151 !important;
            color: white !important;
            border-color: #4b5563;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <nav class="sidebar">
        <div class="sidebar-header">
            <a href="<?php echo BASE_URL; ?>dashboard.php" class="sidebar-brand">
                <i class="fas fa-graduation-cap me-2"></i>
                <?php echo APP_NAME; ?>
            </a>
            <div class="mt-2 text-sm opacity-75">
                Welcome, <?php echo htmlspecialchars($currentUser['username'] ?? 'User'); ?>
            </div>
        </div>
        
        <ul class="sidebar-nav list-unstyled">
            <li class="nav-item">
                <a href="<?php echo BASE_URL; ?>dashboard.php" class="nav-link <?php echo $currentPage == 'dashboard' ? 'active' : ''; ?>">
                    <i class="fas fa-tachometer-alt"></i>
                    Dashboard
                </a>
            </li>
            <li class="nav-item">
                <a href="<?php echo BASE_URL; ?>students/index.php" class="nav-link <?php echo strpos($_SERVER['REQUEST_URI'], 'students') !== false ? 'active' : ''; ?>">
                    <i class="fas fa-users"></i>
                    Students
                </a>
            </li>
            <li class="nav-item">
                <a href="<?php echo BASE_URL; ?>attendance/index.php" class="nav-link <?php echo strpos($_SERVER['REQUEST_URI'], 'attendance') !== false ? 'active' : ''; ?>">
                    <i class="fas fa-calendar-check"></i>
                    Attendance
                </a>
            </li>
            <li class="nav-item">
                <a href="<?php echo BASE_URL; ?>finance/index.php" class="nav-link <?php echo strpos($_SERVER['REQUEST_URI'], 'finance') !== false ? 'active' : ''; ?>">
                    <i class="fas fa-money-bill-wave"></i>
                    Finance
                </a>
            </li>
            <li class="nav-item">
                <a href="<?php echo BASE_URL; ?>examination/index.php" class="nav-link <?php echo strpos($_SERVER['REQUEST_URI'], 'examination') !== false ? 'active' : ''; ?>">
                    <i class="fas fa-graduation-cap"></i>
                    Examination
                </a>
            </li>
            <li class="nav-item">
                <a href="<?php echo BASE_URL; ?>system/index.php" class="nav-link <?php echo strpos($_SERVER['REQUEST_URI'], 'system') !== false ? 'active' : ''; ?>">
                    <i class="fas fa-cogs"></i>
                    System
                </a>
            </li>
            <li class="nav-item">
                <a href="<?php echo BASE_URL; ?>demo/professional-design.php" class="nav-link <?php echo strpos($_SERVER['REQUEST_URI'], 'demo') !== false ? 'active' : ''; ?>">
                    <i class="fas fa-palette"></i>
                    Design Demo
                </a>
            </li>
            <li class="nav-item mt-4">
                <a href="<?php echo BASE_URL; ?>auth/logout.php" class="nav-link">
                    <i class="fas fa-sign-out-alt"></i>
                    Logout
                </a>
            </li>
        </ul>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Navbar -->
        <div class="top-navbar">
            <div class="d-flex align-items-center">
                <h1 class="page-title"><?php echo $pageTitle ?? 'Dashboard'; ?></h1>
            </div>
            <div class="d-flex align-items-center">
                <div class="dropdown">
                    <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-2"></i>
                        <?php echo htmlspecialchars($currentUser['username'] ?? 'User'); ?>
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>profile.php"><i class="fas fa-user me-2"></i>Profile</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>auth/logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Content Wrapper -->
        <div class="content-wrapper">
            <!-- Flash Messages -->
            <?php
            $flashMessages = getFlashMessages();
            foreach ($flashMessages as $message):
            ?>
            <div class="alert alert-<?php echo $message['type']; ?> alert-dismissible fade show" role="alert">
                <?php echo htmlspecialchars($message['message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            <?php endforeach; ?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Attendance Management System</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary: #000000;
            --primary-dark: #10b981;
            --secondary: #8b5cf6;
            --success: #10b981;
            --warning: #f59e0b;
            --error: #ef4444;
            --info: #06b6d4;
            --dark: #1e293b;
            --light: #f8fafc;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
            --white: #ffffff;
            --shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1);
            --gradient-primary: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            --gradient-success: linear-gradient(135deg, var(--success) 0%, #059669 100%);
            --gradient-error: linear-gradient(135deg, var(--error) 0%, #dc2626 100%);
            --sidebar-width: 280px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: var(--gray-900);
            line-height: 1.6;
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* Layout Container */
        .layout-container {
            display: flex;
            min-height: 100vh;
        }

        /* Sidebar Navigation */
        .sidebar {
            width: var(--sidebar-width);
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-right: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: var(--shadow-lg);
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            z-index: 1000;
            transition: transform 0.3s ease;
        }

        .sidebar.mobile-hidden {
            transform: translateX(-100%);
        }

        .sidebar-header {
            padding: 2rem 1.5rem;
            border-bottom: 1px solid var(--gray-200);
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 1rem;
            font-size: 1.3rem;
            font-weight: 800;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .logo-icon {
            width: 40px;
            height: 40px;
            background: var(--gradient-primary);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
        }

        .system-status-sidebar {
            padding: 1rem 1.5rem;
            border-bottom: 1px solid var(--gray-200);
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 12px;
            font-size: 0.875rem;
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .status-dot.online { background: var(--success); }
        .status-dot.offline { background: var(--error); animation: none; }
        .status-dot.connecting { background: var(--warning); }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .sidebar-nav {
            padding: 1rem 0;
        }

        .nav-item {
            margin: 0 1rem 0.5rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem 1.5rem;
            color: var(--gray-600);
            text-decoration: none;
            border-radius: 12px;
            transition: all 0.3s ease;
            font-weight: 500;
            position: relative;
            overflow: hidden;
        }

        .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: var(--gradient-primary);
            transition: left 0.3s ease;
            z-index: -1;
            opacity: 0.1;
        }

        .nav-link:hover::before,
        .nav-link.active::before {
            left: 0;
        }

        .nav-link:hover,
        .nav-link.active {
            color: var(--primary);
            background: rgba(99, 102, 241, 0.1);
            transform: translateX(5px);
        }

        .nav-link i {
            width: 20px;
            text-align: center;
        }

        /* ESP32 Testing Panel in Sidebar */
        .testing-panel {
            margin: 1rem;
            padding: 1rem;
            background: rgba(99, 102, 241, 0.1);
            border-radius: 12px;
            border: 2px solid var(--primary);
        }

        .testing-panel h4 {
            margin: 0 0 1rem 0;
            color: var(--primary);
            font-size: 0.875rem;
            text-transform: uppercase;
            font-weight: 700;
        }

        .test-btn {
            width: 100%;
            margin: 0.25rem 0;
            padding: 0.5rem;
            font-size: 0.75rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
        }

        .test-btn.btn-primary { background: var(--primary); color: white; }
        .test-btn.btn-success { background: var(--success); color: white; }
        .test-btn.btn-info { background: var(--info); color: white; }
        .test-btn.btn-warning { background: var(--warning); color: white; }

        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }

        /* Main Content */
        .main-content {
            flex: 1;
            margin-left: var(--sidebar-width);
            min-height: 100vh;
            padding: 2rem;
            transition: margin-left 0.3s ease;
        }

        .main-content.sidebar-collapsed {
            margin-left: 0;
        }

        /* Mobile Header */
        .mobile-header {
            display: none;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            padding: 1rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: var(--shadow);
            margin: -2rem -2rem 2rem -2rem;
        }

        .mobile-header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .mobile-toggle {
            background: var(--primary);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 0.5rem;
            cursor: pointer;
            font-size: 1.2rem;
        }

        /* Panel System */
        .panel {
            display: none;
            animation: fadeInUp 0.5s ease;
        }

        .panel.active {
            display: block;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .panel-header {
            margin-bottom: 2rem;
            text-align: center;
        }

        .panel-title {
            font-size: 2.5rem;
            font-weight: 800;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
        }

        .panel-subtitle {
            color: var(--gray-600);
            font-size: 1.1rem;
            max-width: 600px;
            margin: 0 auto;
        }

        /* Advanced Cards */
        .card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: var(--shadow-xl);
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 2rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-primary);
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid var(--gray-100);
        }

        .card-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--gray-900);
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .card-icon {
            width: 40px;
            height: 40px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            color: white;
        }

        /* Hardware Status Cards */
        .hardware-status {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .hardware-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 2rem;
            text-align: center;
            box-shadow: var(--shadow-lg);
            border: 2px solid transparent;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .hardware-card.ready {
            border-color: var(--success);
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.05) 100%);
        }

        .hardware-card.waiting {
            border-color: var(--warning);
            background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(217, 119, 6, 0.05) 100%);
            animation: pulse-glow 2s infinite;
        }

        .hardware-card.error {
            border-color: var(--error);
            background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(220, 38, 38, 0.05) 100%);
        }

        @keyframes pulse-glow {
            0%, 100% { box-shadow: 0 0 20px rgba(245, 158, 11, 0.3); }
            50% { box-shadow: 0 0 40px rgba(245, 158, 11, 0.6); }
        }

        .hardware-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 1rem;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
            position: relative;
        }

        .hardware-icon.rfid {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .hardware-icon.fingerprint {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .hardware-icon.scanning {
            animation: spin 2s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .hardware-title {
            font-size: 1.25rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: var(--gray-900);
        }

        .hardware-status-text {
            font-size: 1rem;
            font-weight: 500;
            margin-bottom: 1rem;
        }

        .hardware-status-text.ready { color: var(--success); }
        .hardware-status-text.waiting { color: var(--warning); }
        .hardware-status-text.error { color: var(--error); }

        .hardware-details {
            font-size: 0.875rem;
            color: var(--gray-600);
        }

        /* Forms */
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--gray-700);
            margin-bottom: 0.5rem;
        }

        .form-input, .form-select {
            width: 100%;
            padding: 1rem 1.25rem;
            border: 2px solid var(--gray-200);
            border-radius: 12px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: var(--white);
            font-family: inherit;
        }

        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
            transform: translateY(-2px);
        }

        /* Advanced Buttons */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 1rem 2rem;
            border: none;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            position: relative;
            overflow: hidden;
            min-width: 140px;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
            transform: translate(-50%, -50%);
        }

        .btn:hover::before {
            width: 200px;
            height: 200px;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        .btn-primary {
            background: var(--gradient-primary);
            color: white;
            box-shadow: var(--shadow);
        }

        .btn-primary:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl);
        }

        .btn-success {
            background: var(--gradient-success);
            color: white;
            box-shadow: var(--shadow);
        }

        .btn-success:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl);
        }

        .btn-danger {
            background: var(--gradient-error);
            color: white;
            box-shadow: var(--shadow);
        }

        .btn-danger:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl);
        }

        .btn-loading {
            position: relative;
            color: transparent !important;
        }

        .btn-loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            transform: translate(-50%, -50%);
        }

        /* Stats Grid */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(20px);
            border-radius: 16px;
            padding: 1.5rem;
            box-shadow: var(--shadow-lg);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-xl);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .stat-title {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--gray-600);
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .stat-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
            color: white;
        }

        .stat-icon.present { background: var(--gradient-success); }
        .stat-icon.partial { background: linear-gradient(135deg, var(--warning) 0%, #d97706 100%); }
        .stat-icon.absent { background: var(--gradient-error); }
        .stat-icon.total { background: var(--gradient-primary); }

        .stat-value {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--gray-900);
            margin-bottom: 0.25rem;
        }

        .stat-change {
            font-size: 0.875rem;
            color: var(--gray-600);
            font-weight: 500;
        }

        /* Tables */
        .table-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            overflow: hidden;
            box-shadow: var(--shadow-lg);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th {
            background: var(--gradient-primary);
            color: white;
            padding: 1rem;
            text-align: left;
            font-size: 0.875rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .table td {
            padding: 1rem;
            border-bottom: 1px solid var(--gray-200);
            font-size: 0.875rem;
        }

        .table tbody tr {
            transition: all 0.2s ease;
        }

        .table tbody tr:hover {
            background: rgba(99, 102, 241, 0.05);
        }

        .table tbody tr:last-child td {
            border-bottom: none;
        }

        /* Badges */
        .badge {
            display: inline-flex;
            align-items: center;
            padding: 0.375rem 0.875rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .badge-present { background: rgba(16, 185, 129, 0.1); color: var(--success); }
        .badge-partial { background: rgba(245, 158, 11, 0.1); color: var(--warning); }
        .badge-absent { background: rgba(239, 68, 68, 0.1); color: var(--error); }
        .badge-paid { background: rgba(16, 185, 129, 0.1); color: var(--success); }
        .badge-unpaid { background: rgba(239, 68, 68, 0.1); color: var(--error); }
        .badge-eligible { background: rgba(16, 185, 129, 0.1); color: var(--success); }
        .badge-ineligible { background: rgba(239, 68, 68, 0.1); color: var(--error); }
        .badge-active { background: rgba(99, 102, 241, 0.1); color: var(--primary); }

        /* Notifications */
        .notification {
            position: fixed;
            top: 2rem;
            right: 2rem;
            padding: 1rem 1.5rem;
            border-radius: 12px;
            color: white;
            font-weight: 600;
            font-size: 0.875rem;
            z-index: 10000;
            transform: translateX(400px);
            transition: all 0.3s ease;
            box-shadow: var(--shadow-xl);
            display: flex;
            align-items: center;
            gap: 0.75rem;
            max-width: 400px;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success { background: var(--gradient-success); }
        .notification.error { background: var(--gradient-error); }
        .notification.info { background: var(--gradient-primary); }
        .notification.warning { background: linear-gradient(135deg, var(--warning) 0%, #d97706 100%); }

        /* Loading Spinner */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid rgba(99, 102, 241, 0.3);
            border-top: 2px solid var(--primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        /* Real-time Activity Feed */
        .activity-feed {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 16px;
            padding: 1.5rem;
            max-height: 400px;
            overflow-y: auto;
            box-shadow: var(--shadow-lg);
        }

        .activity-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            border-radius: 12px;
            margin-bottom: 0.5rem;
            transition: all 0.2s ease;
            border-left: 4px solid transparent;
        }

        .activity-item:hover {
            background: rgba(99, 102, 241, 0.05);
            border-left-color: var(--primary);
        }

        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
            color: white;
            flex-shrink: 0;
        }

        .activity-content {
            flex: 1;
        }

        .activity-text {
            font-weight: 600;
            color: var(--gray-900);
            margin-bottom: 0.25rem;
        }

        .activity-time {
            font-size: 0.75rem;
            color: var(--gray-500);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .sidebar.mobile-visible {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .mobile-header {
                display: block;
            }

            .hardware-status {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }

            .panel-title {
                font-size: 2rem;
            }

            .card {
                padding: 1.5rem;
            }

            .card-header {
                flex-direction: column;
                gap: 1rem;
            }
        }

        /* Custom Scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--gray-100);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: var(--gray-300);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--gray-400);
        }
    </style>
</head>
<body>
    <div class="layout-container">
        <!-- Sidebar Navigation -->
        <aside class="sidebar" id="sidebar">
            <!-- Logo and System Info -->
            <div class="sidebar-header">
                <div class="logo">
                    <div class="logo-icon">
                        <i class="fas fa-fingerprint"></i>
                    </div>
                    <div>
                        <div>Attendance System</div>
                        <div style="font-size: 0.75rem; font-weight: 400; opacity: 0.7;">v2.3 Enhanced</div>
                    </div>
                </div>
            </div>

            <!-- System Status -->
            <div class="system-status-sidebar">
                <div class="status-item">
                    <div class="status-dot offline" id="esp32-dot"></div>
                    <div>
                        <div style="font-weight: 600;" id="esp32-status">ESP32: Checking...</div>
                        <div style="font-size: 0.75rem; opacity: 0.7;" id="last-update">Never</div>
                    </div>
                </div>
                <div class="status-item">
                    <i class="fas fa-cog" style="width: 8px;"></i>
                    <div>
                        <div style="font-weight: 600;">Mode</div>
                        <div style="font-size: 0.75rem; opacity: 0.7;" id="current-mode">Unknown</div>
                    </div>
                </div>
            </div>

            <!-- Navigation Menu -->
            <nav class="sidebar-nav">
                <div class="nav-item">
                    <a href="#" class="nav-link active" onclick="showPanel('registration', this)">
                        <i class="fas fa-user-plus"></i>
                        <span>Registration</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link" onclick="showPanel('attendance', this)">
                        <i class="fas fa-calendar-check"></i>
                        <span>Attendance</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link" onclick="showPanel('finance', this)">
                        <i class="fas fa-credit-card"></i>
                        <span>Finance</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link" onclick="showPanel('exam', this)">
                        <i class="fas fa-graduation-cap"></i>
                        <span>Examination</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link" onclick="showPanel('system', this)">
                        <i class="fas fa-cogs"></i>
                        <span>System</span>
                    </a>
                </div>
            </nav>

        
        </aside>

        <!-- Main Content Area -->
        <main class="main-content" id="main-content">
            <!-- Mobile Header -->
            <div class="mobile-header">
                <div class="mobile-header-content">
                    <button class="mobile-toggle" onclick="toggleSidebar()">
                        <i class="fas fa-bars"></i>
                    </button>
                    <div class="logo">
                        <div class="logo-icon">
                            <i class="fas fa-fingerprint"></i>
                        </div>
                        Attendance System
                    </div>
                </div>
            </div>

            <!-- Registration Panel -->
            <div id="registration-panel" class="panel active">
                <div class="panel-header">
                    <h2 class="panel-title">
                        <i class="fas fa-user-plus"></i>
                        Student Registration
                    </h2>
                    <p class="panel-subtitle">Register new students with advanced RFID and fingerprint authentication</p>
                </div>

                <!-- Hardware Status -->
                <div class="hardware-status">
                    <div class="hardware-card waiting" id="rfid-card">
                        <div class="hardware-icon rfid" id="rfid-icon">
                            <i class="fas fa-credit-card"></i>
                        </div>
                        <div class="hardware-title">RFID Scanner</div>
                        <div class="hardware-status-text waiting" id="rfid-status-text">Waiting for card...</div>
                        <div class="hardware-details" id="rfid-details">Place RFID card near scanner</div>
                    </div>
                    
                    <div class="hardware-card waiting" id="fingerprint-card">
                        <div class="hardware-icon fingerprint" id="fingerprint-icon">
                            <i class="fas fa-fingerprint"></i>
                        </div>
                        <div class="hardware-title">Fingerprint Scanner</div>
                        <div class="hardware-status-text waiting" id="fingerprint-status-text">Ready to capture</div>
                        <div class="hardware-details" id="fingerprint-details">Place finger on sensor</div>
                    </div>
                </div>

                <!-- Registration Form -->
                <div class="form-grid">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <div class="card-icon" style="background: var(--gradient-primary);">
                                    <i class="fas fa-user"></i>
                                </div>
                                Student Information
                            </h3>
                        </div>
                        <form id="registration-form">
                            <div class="form-group">
                                <label class="form-label" for="student-name">
                                    <i class="fas fa-user"></i>
                                    Full Name
                                </label>
                                <input type="text" id="student-name" class="form-input" placeholder="Enter student full name" required>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label" for="student-email">
                                    <i class="fas fa-envelope"></i>
                                    Email Address
                                </label>
                                <input type="email" id="student-email" class="form-input" placeholder="<EMAIL>" required>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label" for="student-course">
                                    <i class="fas fa-book"></i>
                                    Course
                                </label>
                                <select id="student-course" class="form-select" required>
                                    <option value="">Select Course</option>
                                    <option value="Computer Science">Computer Science</option>
                                    <option value="Engineering">Engineering</option>
                                    <option value="Business">Business</option>
                                    <option value="Mathematics">Mathematics</option>
                                    <option value="Physics">Physics</option>
                                </select>
                            </div>
                            
                            <button type="submit" id="register-btn" class="btn btn-primary" disabled>
                                <i class="fas fa-user-plus"></i>
                                Register Student
                            </button>
                        </form>
                    </div>

                    <!-- Real-time Activity Feed -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <div class="card-icon" style="background: var(--gradient-success);">
                                    <i class="fas fa-activity"></i>
                                </div>
                                Live Activity
                            </h3>
                        </div>
                        <div class="activity-feed" id="activity-feed">
                            <div class="activity-item">
                                <div class="activity-icon" style="background: var(--gradient-primary);">
                                    <i class="fas fa-power-off"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-text">System initialized</div>
                                    <div class="activity-time">Just now</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Registrations -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <div class="card-icon" style="background: var(--gradient-success);">
                                <i class="fas fa-history"></i>
                            </div>
                            Recent Registrations
                        </h3>
                    </div>
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th><i class="fas fa-user"></i> Name</th>
                                    <th><i class="fas fa-envelope"></i> Email</th>
                                    <th><i class="fas fa-book"></i> Course</th>
                                    <th><i class="fas fa-credit-card"></i> RFID ID</th>
                                    <th><i class="fas fa-check-circle"></i> Status</th>
                                    <th><i class="fas fa-calendar"></i> Registered</th>
                                </tr>
                            </thead>
                            <tbody id="registrations-data">
                                <!-- Populated by JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Attendance Panel -->
            <div id="attendance-panel" class="panel">
                <div class="panel-header">
                    <h2 class="panel-title">
                        <i class="fas fa-calendar-check"></i>
                        Attendance Management
                    </h2>
                    <p class="panel-subtitle">Monitor daily attendance and track student check-ins and check-outs</p>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-title">Present Today</div>
                            <div class="stat-icon present">
                                <i class="fas fa-check"></i>
                            </div>
                        </div>
                        <div class="stat-value" id="present-count">0</div>
                        <div class="stat-change">Students attended today</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-title">Partial</div>
                            <div class="stat-icon partial">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                        </div>
                        <div class="stat-value" id="partial-count">0</div>
                        <div class="stat-change">Partial attendance</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-title">Absent</div>
                            <div class="stat-icon absent">
                                <i class="fas fa-times"></i>
                            </div>
                        </div>
                        <div class="stat-value" id="absent-count">0</div>
                        <div class="stat-change">Students absent</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-title">Total Students</div>
                            <div class="stat-icon total">
                                <i class="fas fa-users"></i>
                            </div>
                        </div>
                        <div class="stat-value" id="total-students">0</div>
                        <div class="stat-change">Registered students</div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <div class="card-icon" style="background: var(--gradient-primary);">
                                <i class="fas fa-calendar-day"></i>
                            </div>
                            Today's Attendance
                        </h3>
                        <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
                            <button onclick="generateTestAttendance()" class="btn btn-success">
                                <i class="fas fa-plus"></i>
                                Generate Test Data
                            </button>
                            <button onclick="clearTodayData()" class="btn btn-danger">
                                <i class="fas fa-trash"></i>
                                Clear Today's Data
                            </button>
                        </div>
                    </div>
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Student Name</th>
                                    <th>Email</th>
                                    <th>Check-in Time</th>
                                    <th>Check-out Time</th>
                                    <th>Hours</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody id="attendance-data">
                                <!-- Populated by JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Finance Panel -->
            <div id="finance-panel" class="panel">
                <div class="panel-header">
                    <h2 class="panel-title">
                        <i class="fas fa-credit-card"></i>
                        Finance Management
                    </h2>
                    <p class="panel-subtitle">Manage student payments and exam fee requirements</p>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <div class="card-icon" style="background: var(--gradient-primary);">
                                <i class="fas fa-cog"></i>
                            </div>
                            Payment Settings
                        </h3>
                    </div>
                    <div style="display: flex; gap: 1rem; align-items: end; flex-wrap: wrap;">
                        <div class="form-group" style="margin-bottom: 0;">
                            <label class="form-label" for="global-threshold">
                                <i class="fas fa-dollar-sign"></i>
                                Global Exam Fee (FRW)
                            </label>
                            <input type="number" id="global-threshold" class="form-input" value="100" min="0" style="width: 200px;">
                        </div>
                        <button onclick="updateGlobalThreshold()" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            Update All Students
                        </button>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <div class="card-icon" style="background: var(--gradient-success);">
                                <i class="fas fa-money-bill-wave"></i>
                            </div>
                            Student Payment Status
                        </h3>
                        <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
                            <button onclick="markAllPaid()" class="btn btn-success">
                                <i class="fas fa-check"></i>
                                Mark All as Paid
                            </button>
                            <button onclick="markAllUnpaid()" class="btn btn-danger">
                                <i class="fas fa-times"></i>
                                Mark All as Unpaid
                            </button>
                            <button onclick="generatePaymentReport()" class="btn btn-primary">
                                <i class="fas fa-download"></i>
                                Export Report
                            </button>
                        </div>
                    </div>
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Student Name</th>
                                    <th>Email</th>
                                    <th>Required Amount</th>
                                    <th>Amount Paid</th>
                                    <th>Balance</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="finance-data">
                                <!-- Populated by JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Exam Panel -->
            <div id="exam-panel" class="panel">
                <div class="panel-header">
                    <h2 class="panel-title">
                        <i class="fas fa-graduation-cap"></i>
                        Examination Management
                    </h2>
                    <p class="panel-subtitle">Control exam access based on attendance and payment eligibility</p>
                </div>
                <div class="form-grid"></div>
                <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <div class="card-icon" style="background: var(--gradient-primary);">
                        <i class="fas fa-cog"></i>
                    </div>
                    Exam Configuration
                </h3>
            </div>
            <div class="form-group">
                <label class="form-label" for="course-name">
                    <i class="fas fa-book"></i>
                    Course Name
                </label>
                <input type="text" id="course-name" class="form-input" placeholder="Enter course name for exam">
            </div>
            
            <div class="form-group">
                <label class="form-label" for="exam-date">
                    <i class="fas fa-calendar"></i>
                    Exam Date
                </label>
                <input type="date" id="exam-date" class="form-input">
            </div>
            
            <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
                <button id="setup-exam-btn" onclick="setupExam()" class="btn btn-primary">
                    <i class="fas fa-cog"></i>
                    Setup Exam
                </button>
                <button id="start-exam-btn" onclick="startExamSession()" class="btn btn-success" disabled>
                    <i class="fas fa-play"></i>
                    Start Session
                </button>
                <button id="end-exam-btn" onclick="endExamSession()" class="btn btn-danger" disabled>
                    <i class="fas fa-stop"></i>
                    End Session
                </button>
            </div>
            <button onclick="sendWarningEmailsReal()" class="btn btn-warning" 
            style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);">
        <i class="fas fa-envelope"></i>
        Send Warning Emails
    </button>
        </div>

    
    
    <!-- Email Action Buttons -->
    <div style="display: flex; gap: 0.5rem; flex-wrap: wrap; margin-bottom: 20px;">
        <!-- Test Email First -->
        <button onclick="sendTestWarningEmail()" class="btn btn-info">
            <i class="fas fa-flask"></i>
            Send Test Email
        </button>
        
        <!-- Preview Who Gets Emails -->
        <button onclick="previewWarningEmails()" class="btn btn-primary">
            <i class="fas fa-eye"></i>
            Preview Recipients
        </button>
        
        <!-- Send Real Warning Emails -->
        <button onclick="sendWarningEmailsReal()" class="btn btn-warning" 
                style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);">
            <i class="fas fa-envelope"></i>
            Send Warning Emails
        </button>
    </div>
    
    <!-- Email System Status -->
    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
        <h5><i class="fas fa-info-circle"></i> Email System Status</h5>
        <div id="email-status-display">
           
        </div>
    </div>
</div>

<!-- Email Testing Instructions -->
<div class="card">
    <div class="card-header">
        <h3 class="card-title">
            <div class="card-icon" style="background: var(--gradient-info);">
                <i class="fas fa-question-circle"></i>
            </div>
            Email Testing Guide
        </h3>
    </div>
    
    

                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <div class="card-icon" style="background: var(--gradient-success);">
                                    <i class="fas fa-fingerprint"></i>
                                </div>
                                Testing Tools
                            </h3>
                        </div>
                        <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
                            <button onclick="simulateExamFingerprint('eligible')" class="btn btn-success">
                                <i class="fas fa-check"></i>
                                Eligible Student
                            </button>
                            <button onclick="simulateExamFingerprint('ineligible')" class="btn btn-danger">
                                <i class="fas fa-times"></i>
                                Ineligible Student
                            </button>
                        </div>
                    </div>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-title">Eligible</div>
                            <div class="stat-icon present">
                                <i class="fas fa-check"></i>
                            </div>
                        </div>
                        <div class="stat-value" id="eligible-count">0</div>
                        <div class="stat-change">Students eligible</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-title">Ineligible</div>
                            <div class="stat-icon absent">
                                <i class="fas fa-times"></i>
                            </div>
                        </div>
                        <div class="stat-value" id="ineligible-count">0</div>
                        <div class="stat-change">Students ineligible</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-title">Attended</div>
                            <div class="stat-icon total">
                                <i class="fas fa-graduation-cap"></i>
                            </div>
                        </div>
                        <div class="stat-value" id="attendee-count">0</div>
                        <div class="stat-change">Exam attendees</div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <div class="card-icon" style="background: var(--gradient-primary);">
                                <i class="fas fa-list"></i>
                            </div>
                            Student Eligibility Status
                        </h3>
                    </div>
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Student Name</th>
                                    <th>Course</th>
                                    <th>Attendance Status</th>
                                    <th>Payment Status</th>
                                    <th>Eligible</th>
                                    <th>Exam Status</th>
                                </tr>
                            </thead>
                            <tbody id="exam-data">
                                <!-- Populated by JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- System Panel -->
            <div id="system-panel" class="panel">
                <div class="panel-header">
                    <h2 class="panel-title">
                        <i class="fas fa-cogs"></i>
                        System Management
                    </h2>
                    <p class="panel-subtitle">Monitor ESP32 hardware and system performance</p>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <div class="card-icon" style="background: var(--gradient-primary);">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            System Status
                        </h3>
                        <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
                            <button onclick="refreshSystemStatus()" class="btn btn-primary">
                                <i class="fas fa-sync"></i>
                                Refresh Status
                            </button>
                            <button onclick="testSystemConnection()" class="btn btn-success">
                                <i class="fas fa-plug"></i>
                                Test Connection
                            </button>
                            <button onclick="clearSystemLogs()" class="btn btn-danger">
                                <i class="fas fa-trash"></i>
                                Clear Logs
                            </button>
                        </div>
                    </div>
                    
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-header">
                                <div class="stat-title">ESP32 Status</div>
                                <div class="stat-icon total">
                                    <i class="fas fa-microchip"></i>
                                </div>
                            </div>
                            <div class="stat-value" id="system-esp32-status">Checking...</div>
                            <div class="stat-change">Hardware Status</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-header">
                                <div class="stat-title">Current Mode</div>
                                <div class="stat-icon present">
                                    <i class="fas fa-cog"></i>
                                </div>
                            </div>
                            <div class="stat-value" id="system-current-mode">Unknown</div>
                            <div class="stat-change">Operating Mode</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-header">
                                <div class="stat-title">Uptime</div>
                                <div class="stat-icon partial">
                                    <i class="fas fa-clock"></i>
                                </div>
                            </div>
                            <div class="stat-value" id="system-uptime">0s</div>
                            <div class="stat-change">System Runtime</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-header">
                                <div class="stat-title">WiFi Signal</div>
                                <div class="stat-icon absent">
                                    <i class="fas fa-wifi"></i>
                                </div>
                            </div>
                            <div class="stat-value" id="system-wifi-signal">N/A</div>
                            <div class="stat-change">Signal Strength</div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <div class="card-icon" style="background: var(--gradient-success);">
                                <i class="fas fa-list"></i>
                            </div>
                            Recent System Logs
                        </h3>
                    </div>
                    <div class="activity-feed" id="system-logs">
                        <!-- Populated by JavaScript -->
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Notification Container -->
    <div id="notification" class="notification"></div>

    <script>
        // ==========================================
        // Global Variables
        // ==========================================
        let currentRFID = null;
        let currentFingerprintID = null;
        let examSessionActive = false;
        let students = [];
        let attendanceData = [];
        let financeData = [];
        let examData = [];
        let systemData = {};
        let lastHeartbeat = null;
        let esp32Connected = false;
        let lastProcessedLogId = 0;
        let esp32DataCheckInterval;
        let heartbeatCheckInterval;
        
        // API Base URL
        const API_BASE = 'api/';
        
        // ==========================================
        // ENHANCED ESP32 Real-time Monitoring (FIXED)
        // ==========================================
        
        /**
         * FIXED: Enhanced ESP32 monitoring with better polling
         */
        function startESP32Monitoring() {
            console.log("🔄 Starting enhanced ESP32 monitoring...");
            
            // Check ESP32 status every 3 seconds
            heartbeatCheckInterval = setInterval(checkESP32Status, 3000);
            
            // Check for NEW ESP32 data every 500ms (faster polling)
            esp32DataCheckInterval = setInterval(pollForNewESP32Data, 500);
            
            // Initial check
            checkESP32Status();
            pollForNewESP32Data();
        }

        /**
         * FIXED: Poll for new ESP32 events more efficiently
         */
        async function pollForNewESP32Data() {
            if (!esp32Connected) return;
            
            try {
                // Get only recent logs since last processed
                const response = await fetch(API_BASE + `system.php?action=logs&limit=10&offset=0`);
                const data = await response.json();
                
                if (data.success && data.data && data.data.length > 0) {
                    // Process only new logs
                    const newLogs = data.data.filter(log => log.log_id > lastProcessedLogId);
                    
                    if (newLogs.length > 0) {
                        console.log("📡 New ESP32 events detected:", newLogs.length);
                        
                        // Update last processed ID
                        lastProcessedLogId = Math.max(...newLogs.map(log => log.log_id));
                        
                        // Process each new event
                        newLogs.reverse().forEach(log => {
                            processESP32Event(log);
                        });
                    }
                }
            } catch (error) {
                console.error('❌ ESP32 data polling failed:', error);
            }
        }

        /**
         * FIXED: Process individual ESP32 events
         */
        function processESP32Event(log) {
            console.log("🔍 Processing ESP32 event:", log);
            
            const eventData = log.event_data || {};
            
            switch (log.event_type) {
                case 'rfid_scan':
                    if (eventData.rfid_id) {
                        console.log("📱 RFID detected:", eventData.rfid_id);
                        handleRFIDDetected(eventData.rfid_id);
                        
                        // Add to activity feed
                        addActivityLog(
                            `RFID card scanned: ${eventData.rfid_id}`, 
                            'fas fa-credit-card', 
                            'var(--gradient-primary)'
                        );
                    }
                    break;
                    
                case 'fingerprint_capture':
                    if (eventData.fingerprint_id) {
                        console.log("👆 Fingerprint detected:", eventData.fingerprint_id);
                        handleFingerprintCaptured(eventData.fingerprint_id);
                        
                        // Add to activity feed
                        addActivityLog(
                            `Fingerprint captured: ID ${eventData.fingerprint_id}`, 
                            'fas fa-fingerprint', 
                            'var(--gradient-success)'
                        );
                    }
                    break;
                    
                case 'heartbeat':
                    console.log("💓 ESP32 heartbeat received");
                    updateESP32Status(eventData);
                    break;
                    
                case 'mode_change':
                    if (eventData.mode) {
                        console.log("🔄 Mode changed to:", eventData.mode);
                        updateCurrentMode(eventData.mode);
                    }
                    break;
                    
                default:
                    console.log("ℹ️  Other ESP32 event:", log.event_type, eventData);
            }
        }

        /**
         * FIXED: Enhanced RFID detection handler
         */
        function handleRFIDDetected(rfidId) {
            console.log("📱 Processing RFID:", rfidId);
            
            currentRFID = rfidId;
            
            // Update RFID card status immediately
            const rfidCard = document.getElementById('rfid-card');
            const rfidIcon = document.getElementById('rfid-icon');
            const rfidStatusText = document.getElementById('rfid-status-text');
            const rfidDetails = document.getElementById('rfid-details');
            
            if (rfidCard && rfidIcon && rfidStatusText && rfidDetails) {
                rfidCard.className = 'hardware-card ready';
                rfidIcon.classList.remove('scanning');
                rfidStatusText.textContent = 'Card Detected!';
                rfidStatusText.className = 'hardware-status-text ready';
                rfidDetails.textContent = `RFID: ${rfidId}`;
                
                // Play success sound
                playNotificationSound('success');
                
                // Show success notification
                showNotification(`RFID card detected: ${rfidId}`, 'success');
                
                // Enable registration form if both RFID and fingerprint are ready
                if (document.getElementById('registration-panel').classList.contains('active')) {
                    enableRegistrationForm();
                }
                
                // Reset after 5 seconds
                setTimeout(() => {
                    if (rfidCard && rfidStatusText && rfidDetails) {
                        rfidCard.className = 'hardware-card waiting';
                        rfidStatusText.textContent = 'Waiting for card...';
                        rfidStatusText.className = 'hardware-status-text waiting';
                        rfidDetails.textContent = 'Place RFID card near scanner';
                    }
                }, 5000);
            }
        }

        /**
         * FIXED: Enhanced fingerprint detection handler
         */
        function handleFingerprintCaptured(fingerprintId) {
            console.log("👆 Processing fingerprint:", fingerprintId);
            
            currentFingerprintID = parseInt(fingerprintId);
            
            // Update fingerprint card status immediately
            const fingerprintCard = document.getElementById('fingerprint-card');
            const fingerprintIcon = document.getElementById('fingerprint-icon');
            const fingerprintStatusText = document.getElementById('fingerprint-status-text');
            const fingerprintDetails = document.getElementById('fingerprint-details');
            
            if (fingerprintCard && fingerprintIcon && fingerprintStatusText && fingerprintDetails) {
                fingerprintCard.className = 'hardware-card ready';
                fingerprintIcon.classList.remove('scanning');
                fingerprintStatusText.textContent = 'Captured Successfully!';
                fingerprintStatusText.className = 'hardware-status-text ready';
                fingerprintDetails.textContent = `Fingerprint ID: ${fingerprintId}`;
                
                // Play success sound
                playNotificationSound('success');
                
                // Show success notification
                showNotification(`Fingerprint captured: ID ${fingerprintId}`, 'success');
                
                // Enable registration form if both RFID and fingerprint are ready
                if (document.getElementById('registration-panel').classList.contains('active')) {
                    enableRegistrationForm();
                }
                
                // Reset after 5 seconds
                setTimeout(() => {
                    if (fingerprintCard && fingerprintStatusText && fingerprintDetails) {
                        fingerprintCard.className = 'hardware-card waiting';
                        fingerprintStatusText.textContent = 'Ready to capture';
                        fingerprintStatusText.className = 'hardware-status-text waiting';
                        fingerprintDetails.textContent = 'Place finger on sensor';
                    }
                }, 5000);
            }
        }

        /**
         * FIXED: Update ESP32 system status
         */
        function updateESP32Status(heartbeatData) {
            const statusDot = document.getElementById('esp32-dot');
            const statusText = document.getElementById('esp32-status');
            const modeText = document.getElementById('current-mode');
            const lastUpdateText = document.getElementById('last-update');
            
            if (statusDot && statusText) {
                statusDot.className = 'status-dot online';
                statusText.textContent = 'ESP32: Connected';
                lastHeartbeat = new Date();
                
                if (lastUpdateText) {
                    lastUpdateText.textContent = 'Just now';
                }
            }
            
            if (modeText && heartbeatData.mode) {
                modeText.textContent = heartbeatData.mode;
            }
            
            // Update system panel if visible
            if (document.getElementById('system-panel').classList.contains('active')) {
                updateSystemDisplay();
            }
        }

        /**
         * FIXED: Update current mode display
         */
        function updateCurrentMode(mode) {
            const modeText = document.getElementById('current-mode');
            if (modeText) {
                modeText.textContent = mode;
            }
            
            // Add activity log
            addActivityLog(`ESP32 mode changed to ${mode}`, 'fas fa-exchange-alt', 'var(--gradient-primary)');
        }

        /**
         * FIXED: Enhanced ESP32 status check with better error handling
         */
        async function checkESP32Status() {
            try {
                const response = await fetch(API_BASE + 'system.php?action=system_status');
                const data = await response.json();
                
                if (data.success && data.data) {
                    const systemStatus = data.data;
                    systemData = systemStatus;
                    esp32Connected = systemStatus.system_online;
                    
                    // Update header status
                    const statusDot = document.getElementById('esp32-dot');
                    const statusText = document.getElementById('esp32-status');
                    const modeText = document.getElementById('current-mode');
                    
                    if (statusDot && statusText) {
                        if (esp32Connected) {
                            statusDot.className = 'status-dot online';
                            statusText.textContent = 'ESP32: Connected';
                            
                            if (modeText && systemStatus.current_mode) {
                                modeText.textContent = systemStatus.current_mode;
                            }
                            
                            lastHeartbeat = new Date();
                        } else {
                            statusDot.className = 'status-dot offline';
                            statusText.textContent = 'ESP32: Disconnected';
                            if (modeText) {
                                modeText.textContent = 'Offline';
                            }
                        }
                    }
                    
                    // Update system panel if visible
                    if (document.getElementById('system-panel')?.classList.contains('active')) {
                        updateSystemDisplay();
                    }
                }
            } catch (error) {
                esp32Connected = false;
                const statusDot = document.getElementById('esp32-dot');
                const statusText = document.getElementById('esp32-status');
                
                if (statusDot && statusText) {
                    statusDot.className = 'status-dot offline';
                    statusText.textContent = 'ESP32: Error';
                }
                
                console.error('❌ ESP32 status check failed:', error);
            }
        }

        /**
         * FIXED: Enable registration form when both RFID and fingerprint are ready
         */
        function enableRegistrationForm() {
            const registerBtn = document.getElementById('register-btn');
            
            if (!registerBtn) return;
            
            if (currentRFID && currentFingerprintID) {
                registerBtn.disabled = false;
                registerBtn.innerHTML = '<i class="fas fa-user-plus"></i> Register Student';
                showNotification('RFID and fingerprint ready! You can now register the student.', 'success');
            } else if (currentRFID) {
                registerBtn.innerHTML = '<i class="fas fa-fingerprint"></i> Waiting for fingerprint...';
                showNotification('RFID detected! Please capture fingerprint.', 'info');
            } else if (currentFingerprintID) {
                registerBtn.innerHTML = '<i class="fas fa-credit-card"></i> Waiting for RFID...';
                showNotification('Fingerprint captured! Please scan RFID card.', 'info');
            }
        }

        // ==========================================
        // TESTING FUNCTIONS - For debugging ESP32 data flow
        // ==========================================

        /**
         * Test function to simulate ESP32 RFID scan
         */
        function simulateRFID(rfidId = '12345678') {
            console.log("🧪 Simulating RFID scan:", rfidId);
            handleRFIDDetected(rfidId);
        }

        /**
         * Test function to simulate ESP32 fingerprint capture
         */
        function simulateFingerprint(fingerprintId = '1') {
            console.log("🧪 Simulating fingerprint capture:", fingerprintId);
            handleFingerprintCaptured(fingerprintId);
        }

        /**
         * FIXED: Debug function to test ESP32 data flow
         */
        function testESP32DataFlow() {
            console.log("🧪 Testing ESP32 data flow...");
            
            // Simulate RFID detection
            console.log("📱 Simulating RFID detection...");
            handleRFIDDetected('TEST12345');
            
            setTimeout(() => {
                // Simulate fingerprint capture
                console.log("👆 Simulating fingerprint capture...");
                handleFingerprintCaptured('5');
            }, 2000);
        }

        /**
         * Check current system state for debugging
         */
        function debugSystemState() {
            console.log("🔍 Current System State:");
            console.log("  ESP32 Connected:", esp32Connected);
            console.log("  Current RFID:", currentRFID);
            console.log("  Current Fingerprint ID:", currentFingerprintID);
            console.log("  Last Processed Log ID:", lastProcessedLogId);
            console.log("  Last Heartbeat:", lastHeartbeat);
            console.log("  System Data:", systemData);
        }

        /**
         * FIXED: Stop monitoring when needed
         */
        function stopESP32Monitoring() {
            if (esp32DataCheckInterval) {
                clearInterval(esp32DataCheckInterval);
                esp32DataCheckInterval = null;
            }
            
            if (heartbeatCheckInterval) {
                clearInterval(heartbeatCheckInterval);
                heartbeatCheckInterval = null;
            }
            
            console.log("⏹️ ESP32 monitoring stopped");
        }

        // ==========================================
        // Initialize System
        // ==========================================
        document.addEventListener('DOMContentLoaded', function() {
            initializeSystem();
            startRealTimeUpdates();
            startESP32Monitoring();
        });
        
        function initializeSystem() {
            console.log("🚀 Initializing enhanced attendance system...");
            
            loadStudents();
            loadAttendanceData();
            loadFinanceData();
            loadExamData();
            updateStats();
            setTodayDate();
            testSystemConnection();
            
            // Initialize last processed log ID from current time
            fetch(API_BASE + 'system.php?action=logs&limit=1')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.data && data.data.length > 0) {
                        lastProcessedLogId = data.data[0].log_id;
                        console.log("📊 Initialized with last log ID:", lastProcessedLogId);
                    }
                })
                .catch(error => {
                    console.log("⚠️ Could not get initial log ID:", error);
                    lastProcessedLogId = 0;
                });
            
            // Add initial activity
            addActivityLog('Enhanced system initialized successfully', 'fas fa-power-off', 'var(--gradient-primary)');
            
            console.log("✅ System initialization complete");
        }
        
        // ==========================================
        // Panel Management (Updated for Sidebar)
        // ==========================================
        function showPanel(panelName, navElement) {
            // Hide all panels
            document.querySelectorAll('.panel').forEach(panel => {
                panel.classList.remove('active');
            });
            
            // Remove active class from all nav links
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });
            
            // Show selected panel
            document.getElementById(panelName + '-panel').classList.add('active');
            
            // Add active class to clicked nav link
            if (navElement) {
                navElement.classList.add('active');
            }
            
            // Load panel-specific data
            if (panelName === 'attendance') {
                refreshAttendanceData();
            } else if (panelName === 'finance') {
                refreshFinanceData();
            } else if (panelName === 'exam') {
                refreshExamData();
            } else if (panelName === 'system') {
                refreshSystemData();
            }
            
            // Add activity log
            addActivityLog(`Switched to ${panelName} panel`, 'fas fa-exchange-alt', 'var(--gradient-primary)');
            
            // Auto-hide sidebar on mobile after navigation
            if (window.innerWidth <= 768) {
                toggleSidebar();
            }
        }

        // ==========================================
        // Mobile Sidebar Toggle
        // ==========================================
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('main-content');
            
            sidebar.classList.toggle('mobile-visible');
            mainContent.classList.toggle('sidebar-collapsed');
        }

        // ==========================================
        // Registration Functions (Enhanced)
        // ==========================================
        document.getElementById('registration-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const registerBtn = document.getElementById('register-btn');
            
            if (!currentRFID) {
                showNotification('Please scan RFID card first', 'error');
                return;
            }
            
            if (!currentFingerprintID) {
                showNotification('Please capture fingerprint first', 'error');
                return;
            }
            
            // Show loading state
            registerBtn.classList.add('btn-loading');
            registerBtn.disabled = true;
            
            const formData = {
                action: 'register',
                name: document.getElementById('student-name').value,
                email: document.getElementById('student-email').value,
                course: document.getElementById('student-course').value,
                rfid_id: currentRFID,
                fingerprint_template: currentFingerprintID
            };
            
            try {
                const response = await fetch(API_BASE + 'students.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showNotification('Student registered successfully!', 'success');
                    addActivityLog(`Student registered: ${formData.name}`, 'fas fa-user-plus', 'var(--gradient-success)');
                    resetRegistrationForm();
                    loadStudents();
                    loadFinanceData();
                } else {
                    showNotification(result.message || 'Registration failed', 'error');
                    addActivityLog(`Registration failed: ${result.message}`, 'fas fa-exclamation-triangle', 'var(--gradient-error)');
                }
            } catch (error) {
                showNotification('Error connecting to server', 'error');
                addActivityLog('Registration error: Server connection failed', 'fas fa-exclamation-triangle', 'var(--gradient-error)');
                console.error('Registration error:', error);
            } finally {
                // Remove loading state
                registerBtn.classList.remove('btn-loading');
                registerBtn.disabled = false;
            }
        });
        
        function resetRegistrationForm() {
            document.getElementById('registration-form').reset();
            document.getElementById('register-btn').disabled = true;
            document.getElementById('register-btn').innerHTML = '<i class="fas fa-user-plus"></i> Register Student';
            currentRFID = null;
            currentFingerprintID = null;
            
            // Reset hardware cards
            const rfidCard = document.getElementById('rfid-card');
            const fingerprintCard = document.getElementById('fingerprint-card');
            
            if (rfidCard) rfidCard.className = 'hardware-card waiting';
            if (fingerprintCard) fingerprintCard.className = 'hardware-card waiting';
            
            const rfidStatusText = document.getElementById('rfid-status-text');
            const rfidDetails = document.getElementById('rfid-details');
            const fingerprintStatusText = document.getElementById('fingerprint-status-text');
            const fingerprintDetails = document.getElementById('fingerprint-details');
            
            if (rfidStatusText) {
                rfidStatusText.textContent = 'Waiting for card...';
                rfidStatusText.className = 'hardware-status-text waiting';
            }
            if (rfidDetails) rfidDetails.textContent = 'Place RFID card near scanner';
            
            if (fingerprintStatusText) {
                fingerprintStatusText.textContent = 'Ready to capture';
                fingerprintStatusText.className = 'hardware-status-text waiting';
            }
            if (fingerprintDetails) fingerprintDetails.textContent = 'Place finger on sensor';
        }
        
        // ==========================================
        // Activity Feed Functions
        // ==========================================
        function addActivityLog(text, iconClass, iconColor) {
            const activityFeed = document.getElementById('activity-feed');
            const activityItem = document.createElement('div');
            activityItem.className = 'activity-item';
            
            const now = new Date();
            const timeString = now.toLocaleTimeString();
            
            activityItem.innerHTML = `
                <div class="activity-icon" style="background: ${iconColor};">
                    <i class="${iconClass}"></i>
                </div>
                <div class="activity-content">
                    <div class="activity-text">${text}</div>
                    <div class="activity-time">${timeString}</div>
                </div>
            `;
            
            // Add to top of feed
            activityFeed.insertBefore(activityItem, activityFeed.firstChild);
            
            // Keep only last 10 items
            while (activityFeed.children.length > 10) {
                activityFeed.removeChild(activityFeed.lastChild);
            }
        }
        
        // ==========================================
        // Notification System (Enhanced)
        // ==========================================
        function showNotification(message, type = 'info') {
            const notification = document.getElementById('notification');
            const iconMap = {
                success: 'fas fa-check-circle',
                error: 'fas fa-exclamation-circle',
                warning: 'fas fa-exclamation-triangle',
                info: 'fas fa-info-circle'
            };
            
            notification.innerHTML = `
                <i class="${iconMap[type]}"></i>
                ${message}
            `;
            notification.className = `notification ${type}`;
            notification.classList.add('show');
            
            setTimeout(() => {
                notification.classList.remove('show');
            }, 4000);
        }
        
        function playNotificationSound(type) {
            // Create audio context for sound effects
            try {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();
                
                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);
                
                if (type === 'success') {
                    oscillator.frequency.value = 800;
                    gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
                    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);
                } else if (type === 'error') {
                    oscillator.frequency.value = 300;
                    gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
                    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);
                }
                
                oscillator.start();
                oscillator.stop(audioContext.currentTime + 0.5);
            } catch (error) {
                // Fallback for browsers that don't support Web Audio API
                console.log('Audio notification:', type);
            }
        }
        
        // ==========================================
        // Real-time Updates
        // ==========================================
        function startRealTimeUpdates() {
            setInterval(updateLastSeen, 1000);
            setInterval(updateStats, 10000);
        }
        
        function updateLastSeen() {
            if (lastHeartbeat) {
                const now = new Date();
                const diff = Math.floor((now - lastHeartbeat) / 1000);
                
                const lastUpdateElement = document.getElementById('last-update');
                if (lastUpdateElement) {
                    if (diff < 60) {
                        lastUpdateElement.textContent = `${diff}s ago`;
                    } else if (diff < 3600) {
                        lastUpdateElement.textContent = `${Math.floor(diff/60)}m ago`;
                    } else {
                        lastUpdateElement.textContent = `${Math.floor(diff/3600)}h ago`;
                    }
                }
            }
        }
        
        function updateSystemDisplay() {
            if (systemData.system_online) {
                const systemStatus = document.getElementById('system-esp32-status');
                if (systemStatus) {
                    systemStatus.textContent = 'Online';
                    systemStatus.style.color = 'var(--success)';
                }
            } else {
                const systemStatus = document.getElementById('system-esp32-status');
                if (systemStatus) {
                    systemStatus.textContent = 'Offline';
                    systemStatus.style.color = 'var(--error)';
                }
            }
            
            const currentModeEl = document.getElementById('system-current-mode');
            if (currentModeEl) {
                currentModeEl.textContent = systemData.current_mode || 'Unknown';
            }
            
            const uptimeEl = document.getElementById('system-uptime');
            if (uptimeEl) {
                uptimeEl.textContent = formatUptime(systemData.esp32_uptime || 0);
            }
            
            const wifiSignalEl = document.getElementById('system-wifi-signal');
            if (wifiSignalEl) {
                wifiSignalEl.textContent = systemData.esp32_wifi_signal ? `${systemData.esp32_wifi_signal} dBm` : 'N/A';
            }
        }
        
        function formatUptime(seconds) {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const secs = seconds % 60;
            
            if (hours > 0) {
                return `${hours}h ${minutes}m`;
            } else if (minutes > 0) {
                return `${minutes}m ${secs}s`;
            } else {
                return `${secs}s`;
            }
        }
        
        // ==========================================
        // Data Loading Functions
        // ==========================================
        async function loadStudents() {
            try {
                const response = await fetch(API_BASE + 'students.php?action=list');
                const data = await response.json();
                
                if (data.success) {
                    students = data.data || [];
                    updateRegistrationsTable();
                    updateStats();
                }
            } catch (error) {
                console.error('Error loading students:', error);
                addActivityLog('Error loading students data', 'fas fa-exclamation-triangle', 'var(--gradient-error)');
            }
        }
        
        function updateRegistrationsTable() {
            const tbody = document.getElementById('registrations-data');
            if (!tbody) return;
            
            tbody.innerHTML = '';
            
            students.slice(-10).reverse().forEach(student => {
                const row = tbody.insertRow();
                const registrationDate = new Date(student.registration_date || student.created_at).toLocaleDateString();
                
                row.innerHTML = `
                    <td><strong>${student.name}</strong></td>
                    <td>${student.email}</td>
                    <td>${student.course}</td>
                    <td><code>${student.rfid_id}</code></td>
                    <td><span class="badge badge-active"><i class="fas fa-check"></i> Active</span></td>
                    <td>${registrationDate}</td>
                `;
            });
        }
        
        async function loadAttendanceData() {
            try {
                const response = await fetch(API_BASE + 'attendance.php?action=today');
                const data = await response.json();
                
                if (data.success) {
                    attendanceData = data.data || [];
                    updateAttendanceTable();
                    updateStats();
                }
            } catch (error) {
                console.error('Error loading attendance:', error);
                addActivityLog('Error loading attendance data', 'fas fa-exclamation-triangle', 'var(--gradient-error)');
            }
        }
        
        function updateAttendanceTable() {
            const tbody = document.getElementById('attendance-data');
            if (!tbody) return;
            
            tbody.innerHTML = '';
            
            attendanceData.forEach(record => {
                const row = tbody.insertRow();
                
                row.innerHTML = `
                    <td><strong>${record.student_name || record.name}</strong></td>
                    <td>${record.email}</td>
                    <td>${record.check_in || '-'}</td>
                    <td>${record.check_out || '-'}</td>
                    <td>${record.hours || '0.0'}h</td>
                    <td><span class="badge badge-${record.status}"><i class="fas fa-${record.status === 'present' ? 'check' : record.status === 'partial' ? 'exclamation-triangle' : 'times'}"></i> ${record.status.charAt(0).toUpperCase() + record.status.slice(1)}</span></td>
                `;
            });
        }
        
        async function loadFinanceData() {
            try {
                const response = await fetch(API_BASE + 'finance.php?action=list');
                const data = await response.json();
                
                if (data.success) {
                    financeData = data.data || [];
                    updateFinanceTable();
                }
            } catch (error) {
                console.error('Error loading finance data:', error);
                addActivityLog('Error loading finance data', 'fas fa-exclamation-triangle', 'var(--gradient-error)');
            }
        }
        
        function updateFinanceTable() {
            const tbody = document.getElementById('finance-data');
            if (!tbody) return;
            
            tbody.innerHTML = '';
            
            financeData.forEach(record => {
                const row = tbody.insertRow();
                const balance = (record.required_amount || 0) - (record.amount_paid || 0);
                
                row.innerHTML = `
                    <td><strong>${record.student_name || record.name}</strong></td>
                    <td>${record.email}</td>
                    <td>$${record.required_amount || 0}</td>
                    <td>$${record.amount_paid || 0}</td>
                    <td>$${balance.toFixed(2)}</td>
                    <td><span class="badge badge-${record.payment_status || 'unpaid'}"><i class="fas fa-${(record.payment_status === 'paid') ? 'check' : 'times'}"></i> ${(record.payment_status || 'unpaid').charAt(0).toUpperCase() + (record.payment_status || 'unpaid').slice(1)}</span></td>
                    <td>
                        <button onclick="updatePayment(${record.student_id})" class="btn btn-primary">
                            <i class="fas fa-edit"></i>
                            Update
                        </button>
                    </td>
                `;
            });
        }
        
        async function loadExamData() {
            try {
                const response = await fetch(API_BASE + 'exam.php?action=eligibility');
                const data = await response.json();
                
                if (data.success) {
                    examData = data.data || [];
                    updateExamTable();
                    updateExamStats();
                }
            } catch (error) {
                console.error('Error loading exam data:', error);
                addActivityLog('Error loading exam data', 'fas fa-exclamation-triangle', 'var(--gradient-error)');
            }
        }
        
        function updateExamTable() {
            const tbody = document.getElementById('exam-data');
            if (!tbody) return;
            
            tbody.innerHTML = '';
            
            examData.forEach(student => {
                const row = tbody.insertRow();
                const eligible = student.overall_eligible ? 'eligible' : 'ineligible';
                const attendanceStatus = student.attendance_eligible ? 'paid' : 'unpaid';
                const financeStatus = student.finance_eligible ? 'paid' : 'unpaid';
                
                row.innerHTML = `
                    <td><strong>${student.name}</strong></td>
                    <td>${student.course}</td>
                    <td><span class="badge badge-${attendanceStatus}"><i class="fas fa-${student.attendance_eligible ? 'check' : 'times'}"></i> ${student.attendance_eligible ? 'Good' : `${student.absent_count} absences`}</span></td>
                    <td><span class="badge badge-${financeStatus}"><i class="fas fa-${student.finance_eligible ? 'check' : 'times'}"></i> $${student.amount_paid}/${student.required_amount}</span></td>
                    <td><span class="badge badge-${eligible}"><i class="fas fa-${eligible === 'eligible' ? 'check' : 'times'}"></i> ${eligible.charAt(0).toUpperCase() + eligible.slice(1)}</span></td>
                    <td><span class="badge badge-absent"><i class="fas fa-times"></i> Not Attended</span></td>
                `;
            });
        }
        
        function updateExamStats() {
            const eligible = examData.filter(s => s.overall_eligible).length;
            const ineligible = examData.filter(s => !s.overall_eligible).length;
            
            const eligibleEl = document.getElementById('eligible-count');
            const ineligibleEl = document.getElementById('ineligible-count');
            const attendeeEl = document.getElementById('attendee-count');
            
            if (eligibleEl) eligibleEl.textContent = eligible;
            if (ineligibleEl) ineligibleEl.textContent = ineligible;
            if (attendeeEl) attendeeEl.textContent = 0;
        }
        
        function updateStats() {
            const totalStudents = students.length;
            const presentToday = attendanceData.filter(a => a.status === 'present').length;
            const partialToday = attendanceData.filter(a => a.status === 'partial').length;
            const absentToday = totalStudents - presentToday - partialToday;
            
            // Update stat cards
            const presentEl = document.getElementById('present-count');
            const partialEl = document.getElementById('partial-count');
            const absentEl = document.getElementById('absent-count');
            const totalEl = document.getElementById('total-students');
            
            if (presentEl) presentEl.textContent = presentToday;
            if (partialEl) partialEl.textContent = partialToday;
            if (absentEl) absentEl.textContent = absentToday;
            if (totalEl) totalEl.textContent = totalStudents;
        }

        // ==========================================
        // Attendance Management Functions
        // ==========================================
        async function refreshAttendanceData() {
            await loadAttendanceData();
            showNotification('Attendance data refreshed', 'info');
        }

        async function generateTestAttendance() {
            try {
                const response = await fetch(API_BASE + 'attendance.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'generate_test_data'
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    showNotification('Test attendance data generated successfully!', 'success');
                    loadAttendanceData();
                    addActivityLog('Test attendance data generated', 'fas fa-plus', 'var(--gradient-success)');
                } else {
                    showNotification(result.message || 'Failed to generate test data', 'error');
                }
            } catch (error) {
                showNotification('Error generating test data', 'error');
                console.error('Generate test data error:', error);
            }
        }

        async function clearTodayData() {
            if (!confirm('Are you sure you want to clear all attendance data for today? This action cannot be undone.')) {
                return;
            }

            try {
                const response = await fetch(API_BASE + 'attendance.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'clear_today'
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    showNotification('Today\'s attendance data cleared successfully!', 'success');
                    loadAttendanceData();
                    addActivityLog('Today\'s attendance data cleared', 'fas fa-trash', 'var(--gradient-error)');
                } else {
                    showNotification(result.message || 'Failed to clear data', 'error');
                }
            } catch (error) {
                showNotification('Error clearing data', 'error');
                console.error('Clear data error:', error);
            }
        }

        // ==========================================
        // Finance Management Functions
        // ==========================================
        async function refreshFinanceData() {
            await loadFinanceData();
            showNotification('Finance data refreshed', 'info');
        }

        async function updateGlobalThreshold() {
            const threshold = document.getElementById('global-threshold').value;
            
            if (!threshold || threshold < 0) {
                showNotification('Please enter a valid threshold amount', 'error');
                return;
            }

            try {
                const response = await fetch(API_BASE + 'finance.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'update_global_threshold',
                        threshold: parseFloat(threshold)
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    showNotification(`Global exam fee updated to ${threshold} for all students!`, 'success');
                    loadFinanceData();
                    addActivityLog(`Global exam fee set to ${threshold}`, 'fas fa-dollar-sign', 'var(--gradient-success)');
                } else {
                    showNotification(result.message || 'Failed to update threshold', 'error');
                }
            } catch (error) {
                showNotification('Error updating threshold', 'error');
                console.error('Update threshold error:', error);
            }
        }

        async function markAllPaid() {
            if (!confirm('Mark all students as fully paid? This will set their payment status to "paid".')) {
                return;
            }

            try {
                const response = await fetch(API_BASE + 'finance.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'mark_all_paid'
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    showNotification('All students marked as paid!', 'success');
                    loadFinanceData();
                    loadExamData(); // Refresh exam eligibility
                    addActivityLog('All students marked as paid', 'fas fa-check', 'var(--gradient-success)');
                } else {
                    showNotification(result.message || 'Failed to update payments', 'error');
                }
            } catch (error) {
                showNotification('Error updating payments', 'error');
                console.error('Mark all paid error:', error);
            }
        }

        async function markAllUnpaid() {
            if (!confirm('Mark all students as unpaid? This will reset their payment amounts to $0.')) {
                return;
            }

            try {
                const response = await fetch(API_BASE + 'finance.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'mark_all_unpaid'
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    showNotification('All students marked as unpaid!', 'success');
                    loadFinanceData();
                    loadExamData(); // Refresh exam eligibility
                    addActivityLog('All students marked as unpaid', 'fas fa-times', 'var(--gradient-error)');
                } else {
                    showNotification(result.message || 'Failed to update payments', 'error');
                }
            } catch (error) {
                showNotification('Error updating payments', 'error');
                console.error('Mark all unpaid error:', error);
            }
        }

        async function updatePayment(studentId) {
            const student = financeData.find(s => s.student_id === studentId);
            if (!student) {
                showNotification('Student not found', 'error');
                return;
            }

            const newAmount = prompt(`Update payment for ${student.student_name || student.name}:\nRequired: ${student.required_amount}\nCurrent: ${student.amount_paid}\n\nEnter new amount paid:`, student.amount_paid);
            
            if (newAmount === null) return; // User cancelled
            
            const amount = parseFloat(newAmount);
            if (isNaN(amount) || amount < 0) {
                showNotification('Please enter a valid amount', 'error');
                return;
            }

            try {
                const response = await fetch(API_BASE + 'finance.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'update_payment',
                        student_id: studentId,
                        amount_paid: amount
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    showNotification(`Payment updated for ${student.student_name || student.name}!`, 'success');
                    loadFinanceData();
                    loadExamData(); // Refresh exam eligibility
                    addActivityLog(`Payment updated: ${student.student_name || student.name} - ${amount}`, 'fas fa-dollar-sign', 'var(--gradient-success)');
                } else {
                    showNotification(result.message || 'Failed to update payment', 'error');
                }
            } catch (error) {
                showNotification('Error updating payment', 'error');
                console.error('Update payment error:', error);
            }
        }

        function generatePaymentReport() {
            if (financeData.length === 0) {
                showNotification('No finance data available to export', 'warning');
                return;
            }

            // Create CSV content
            const csvContent = [
                ['Student Name', 'Email', 'Course', 'Required Amount', 'Amount Paid', 'Balance', 'Payment Status'],
                ...financeData.map(record => [
                    record.student_name || record.name,
                    record.email,
                    record.course || '',
                    record.required_amount || 0,
                    record.amount_paid || 0,
                    (record.required_amount || 0) - (record.amount_paid || 0),
                    record.payment_status || 'unpaid'
                ])
            ].map(row => row.map(cell => `"${cell}"`).join(',')).join('\n');

            // Create download link
            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `payment_report_${new Date().toISOString().split('T')[0]}.csv`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);

            showNotification('Payment report exported successfully!', 'success');
            addActivityLog('Payment report exported', 'fas fa-download', 'var(--gradient-primary)');
        }

        // ==========================================
        // Exam Management Functions
        // ==========================================
        async function refreshExamData() {
            await loadExamData();
            showNotification('Exam data refreshed', 'info');
        }

        function setupExam() {
            const courseName = document.getElementById('course-name').value;
            const examDate = document.getElementById('exam-date').value;

            if (!courseName || !examDate) {
                showNotification('Please fill in course name and exam date', 'error');
                return;
            }

            // Enable start button
            document.getElementById('start-exam-btn').disabled = false;
            document.getElementById('setup-exam-btn').innerHTML = '<i class="fas fa-check"></i> Exam Setup Complete';
            
            showNotification(`Exam setup complete for ${courseName} on ${examDate}`, 'success');
            addActivityLog(`Exam setup: ${courseName} on ${examDate}`, 'fas fa-cog', 'var(--gradient-primary)');
        }

        function startExamSession() {
            examSessionActive = true;
            
            // Update button states
            document.getElementById('start-exam-btn').disabled = true;
            document.getElementById('end-exam-btn').disabled = false;
            
            showNotification('Exam session started! Students can now access the exam.', 'success');
            addActivityLog('Exam session started', 'fas fa-play', 'var(--gradient-success)');
        }

        function endExamSession() {
            if (!confirm('Are you sure you want to end the exam session? Students will no longer be able to access the exam.')) {
                return;
            }

            examSessionActive = false;
            
            // Update button states
            document.getElementById('start-exam-btn').disabled = false;
            document.getElementById('end-exam-btn').disabled = true;
            
            showNotification('Exam session ended.', 'info');
            addActivityLog('Exam session ended', 'fas fa-stop', 'var(--gradient-error)');
        }

        /**
 * REAL EMAIL FUNCTIONS - These send actual emails!
 */

/**
 * Send a test warning email to yourself first
 */
// Find your existing sendWarningEmails() function in index.html and REPLACE it with these:

/**
 * Send a test warning email to yourself first
 */
async function sendTestWarningEmail() {
    const testEmail = prompt(
        "Enter YOUR email address to receive a test warning email:\n\n" +
        "This will send a REAL email to verify the system works.",
        "<EMAIL>"  // Your email as default
    );
    
    if (!testEmail || !testEmail.includes('@')) {
        showNotification('Please enter a valid test email address', 'warning');
        return;
    }
    
    try {
        const response = await fetch(API_BASE + 'exam.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'send_test_warning',
                test_email: testEmail
            })
        });

        const result = await response.json();
        
        if (result.success) {
            showNotification(`📧 Test warning email sent to ${testEmail}! Check your inbox.`, 'success');
            addActivityLog(`Test warning email sent to ${testEmail}`, 'fas fa-envelope', 'var(--gradient-success)');
        } else {
            showNotification(result.message || 'Failed to send test email', 'error');
        }
    } catch (error) {
        showNotification('Error sending test email', 'error');
        console.error('Send test email error:', error);
    }
}

/**
 * Send warning emails to all ineligible students (REAL EMAILS!)
 */
async function sendWarningEmailsReal() {
    // First, show who will receive emails
    const response = await fetch(API_BASE + 'exam.php?action=eligibility');
    const result = await response.json();
    
    if (!result.success) {
        showNotification('Could not load student data', 'error');
        return;
    }
    
    const ineligibleStudents = result.data.filter(student => !student.overall_eligible);
    
    if (ineligibleStudents.length === 0) {
        showNotification('🎉 All students are eligible! No warning emails needed.', 'success');
        return;
    }
    
    // Show confirmation with student list
    let confirmMessage = `⚠️ REAL EMAIL WARNING ⚠️\n\n`;
    confirmMessage += `This will send REAL WARNING EMAILS to ${ineligibleStudents.length} students:\n\n`;
    
    ineligibleStudents.slice(0, 5).forEach(student => {
        confirmMessage += `• ${student.name} (${student.email})\n`;
    });
    
    if (ineligibleStudents.length > 5) {
        confirmMessage += `... and ${ineligibleStudents.length - 5} more students\n`;
    }
    
    confirmMessage += `\nAre you sure you want to send these REAL emails?\n\n`;
    confirmMessage += `Click OK to send REAL emails, or Cancel to stop.`;
    
    const confirmed = confirm(confirmMessage);
    
    if (!confirmed) {
        showNotification('Email sending cancelled', 'info');
        return;
    }
    
    // Final confirmation
    const doubleConfirm = confirm(
        `FINAL CONFIRMATION:\n\n` +
        `Send ${ineligibleStudents.length} REAL warning emails now?\n\n` +
        `This action cannot be undone!`
    );
    
    if (!doubleConfirm) {
        showNotification('Email sending cancelled', 'info');
        return;
    }
    
    try {
        const sendResponse = await fetch(API_BASE + 'exam.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'send_warnings'  // Fixed action name
            })
        });

        const sendResult = await sendResponse.json();
        
        if (sendResult.success) {
            showNotification(`📧 ${sendResult.count || 0} warning emails sent successfully!`, 'success');
            addActivityLog(`Warning emails sent to ${sendResult.count || 0} students`, 'fas fa-envelope', 'var(--gradient-warning)');
            
            // Log the details
            console.log(`✅ Successfully sent ${sendResult.count} warning emails`);
            ineligibleStudents.forEach(student => {
                console.log(`📧 Email sent to: ${student.name} (${student.email})`);
            });
            
        } else {
            showNotification(sendResult.message || 'Failed to send warning emails', 'error');
        }
    } catch (error) {
        showNotification('Error sending warning emails', 'error');
        console.error('Send warning emails error:', error);
    }
}

// Keep your existing previewWarningEmails() function as it is

/**
 * Send warning emails to all ineligible students (REAL EMAILS!)
 */
async function sendWarningEmailsReal() {
    // First, show who will receive emails
    const response = await fetch(API_BASE + 'exam.php?action=eligibility');
    const result = await response.json();
    
    if (!result.success) {
        showNotification('Could not load student data', 'error');
        return;
    }
    
    const ineligibleStudents = result.data.filter(student => !student.overall_eligible);
    
    if (ineligibleStudents.length === 0) {
        showNotification('🎉 All students are eligible! No warning emails needed.', 'success');
        return;
    }
    
    // Show confirmation with student list
    let confirmMessage = `⚠️ REAL EMAIL WARNING ⚠️\n\n`;
    confirmMessage += `This will send REAL WARNING EMAILS to ${ineligibleStudents.length} students:\n\n`;
    
    ineligibleStudents.slice(0, 5).forEach(student => {
        confirmMessage += `• ${student.name} (${student.email})\n`;
    });
    
    if (ineligibleStudents.length > 5) {
        confirmMessage += `... and ${ineligibleStudents.length - 5} more students\n`;
    }
    
    confirmMessage += `\nAre you sure you want to send these REAL emails?\n\n`;
    confirmMessage += `Click OK to send REAL emails, or Cancel to stop.`;
    
    const confirmed = confirm(confirmMessage);
    
    if (!confirmed) {
        showNotification('Email sending cancelled', 'info');
        return;
    }
    
    // Final confirmation
    const doubleConfirm = confirm(
        `FINAL CONFIRMATION:\n\n` +
        `Send ${ineligibleStudents.length} REAL warning emails now?\n\n` +
        `This action cannot be undone!`
    );
    
    if (!doubleConfirm) {
        showNotification('Email sending cancelled', 'info');
        return;
    }
    
    try {
        const sendResponse = await fetch(API_BASE + 'exam.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'send_warnings'  // Fixed action name
            })
        });

        const sendResult = await sendResponse.json();
        
        if (sendResult.success) {
            showNotification(`📧 ${sendResult.count || 0} warning emails sent successfully!`, 'success');
            addActivityLog(`Warning emails sent to ${sendResult.count || 0} students`, 'fas fa-envelope', 'var(--gradient-warning)');
            
            // Log the details
            console.log(`✅ Successfully sent ${sendResult.count} warning emails`);
            ineligibleStudents.forEach(student => {
                console.log(`📧 Email sent to: ${student.name} (${student.email})`);
            });
            
        } else {
            showNotification(sendResult.message || 'Failed to send warning emails', 'error');
        }
    } catch (error) {
        showNotification('Error sending warning emails', 'error');
        console.error('Send warning emails error:', error);
    }
}

/**
 * Preview warning emails (safe - shows info only)
 */
async function previewWarningEmails() {
    try {
        const response = await fetch(API_BASE + 'exam.php?action=eligibility');
        const result = await response.json();
        
        if (result.success && result.data) {
            const ineligibleStudents = result.data.filter(student => !student.overall_eligible);
            
            console.log("📋 Students who will receive warning emails:");
            console.table(ineligibleStudents.map(student => ({
                Name: student.name,
                Email: student.email,
                Course: student.course,
                Reason: student.ineligible_reason || 'Not eligible'
            })));
            
            if (ineligibleStudents.length > 0) {
                showNotification(`📋 Preview: ${ineligibleStudents.length} students will receive warning emails`, 'info');
                
                // Show first few in notification
                let previewText = "Students who will receive warnings:\n";
                ineligibleStudents.slice(0, 3).forEach(student => {
                    previewText += `• ${student.name} (${student.email})\n`;
                });
                if (ineligibleStudents.length > 3) {
                    previewText += `... and ${ineligibleStudents.length - 3} more (see console)`;
                }
                
                console.log("📧 Email Preview Details:");
                ineligibleStudents.forEach(student => {
                    console.log(`📧 TO: ${student.name} (${student.email})`);
                    console.log(`   REASON: ${student.ineligible_reason}`);
                    console.log(`   COURSE: ${student.course}`);
                    console.log("---");
                });
            } else {
                showNotification('🎉 All students are eligible - no warning emails needed!', 'success');
            }
        }
    } catch (error) {
        console.error('Error previewing emails:', error);
        showNotification('Error previewing warning emails', 'error');
    }
}

        function simulateExamFingerprint(type) {
            if (type === 'eligible') {
                // Find an eligible student
                const eligible = examData.find(s => s.overall_eligible);
                if (eligible) {
                    showNotification(`✅ Access Granted: ${eligible.name} is eligible for the exam`, 'success');
                    addActivityLog(`Exam access granted: ${eligible.name}`, 'fas fa-check', 'var(--gradient-success)');
                } else {
                    showNotification('✅ Access Granted: Simulated eligible student', 'success');
                }
            } else {
                // Find an ineligible student
                const ineligible = examData.find(s => !s.overall_eligible);
                if (ineligible) {
                    const reason = !ineligible.attendance_eligible ? 'Poor attendance' : 'Payment required';
                    showNotification(`❌ Access Denied: ${ineligible.name} - ${reason}`, 'error');
                    addActivityLog(`Exam access denied: ${ineligible.name} - ${reason}`, 'fas fa-times', 'var(--gradient-error)');
                } else {
                    showNotification('❌ Access Denied: Simulated ineligible student', 'error');
                }
            }
        }

        // ==========================================
        // System Management Functions
        // ==========================================
        async function refreshSystemData() {
            await refreshSystemStatus();
            await loadSystemLogs();
            showNotification('System data refreshed', 'info');
        }

        async function refreshSystemStatus() {
            try {
                const response = await fetch(API_BASE + 'system.php?action=system_status');
                const data = await response.json();
                
                if (data.success && data.data) {
                    systemData = data.data;
                    updateSystemDisplay();
                }
            } catch (error) {
                console.error('Error refreshing system status:', error);
            }
        }

        async function testSystemConnection() {
            try {
                const response = await fetch(API_BASE + 'system.php?action=test_connection');
                const data = await response.json();
                
                if (data.success) {
                    showNotification('✅ System connection test successful!', 'success');
                    addActivityLog('System connection test passed', 'fas fa-plug', 'var(--gradient-success)');
                } else {
                    showNotification('❌ System connection test failed', 'error');
                    addActivityLog('System connection test failed', 'fas fa-exclamation-triangle', 'var(--gradient-error)');
                }
            } catch (error) {
                showNotification('❌ Cannot connect to system', 'error');
                addActivityLog('System connection error', 'fas fa-exclamation-triangle', 'var(--gradient-error)');
                console.error('Test connection error:', error);
            }
        }

        async function clearSystemLogs() {
            if (!confirm('Are you sure you want to clear all system logs? This action cannot be undone.')) {
                return;
            }

            try {
                const response = await fetch(API_BASE + 'system.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'clear_logs'
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    showNotification('System logs cleared successfully!', 'success');
                    loadSystemLogs();
                    addActivityLog('System logs cleared', 'fas fa-trash', 'var(--gradient-error)');
                } else {
                    showNotification(result.message || 'Failed to clear logs', 'error');
                }
            } catch (error) {
                showNotification('Error clearing logs', 'error');
                console.error('Clear logs error:', error);
            }
        }

        async function loadSystemLogs() {
            try {
                const response = await fetch(API_BASE + 'system.php?action=logs&limit=20');
                const data = await response.json();
                
                if (data.success && data.data) {
                    updateSystemLogsDisplay(data.data);
                }
            } catch (error) {
                console.error('Error loading system logs:', error);
            }
        }

        function updateSystemLogsDisplay(logs) {
            const systemLogsContainer = document.getElementById('system-logs');
            if (!systemLogsContainer) return;
            
            systemLogsContainer.innerHTML = '';
            
            if (logs.length === 0) {
                systemLogsContainer.innerHTML = `
                    <div class="activity-item">
                        <div class="activity-icon" style="background: var(--gradient-primary);">
                            <i class="fas fa-info"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-text">No system logs available</div>
                            <div class="activity-time">System clean</div>
                        </div>
                    </div>
                `;
                return;
            }

            logs.forEach(log => {
                const logItem = document.createElement('div');
                logItem.className = 'activity-item';
                
                const eventData = log.event_data ? JSON.parse(log.event_data) : {};
                const iconMap = {
                    'rfid_scan': 'fas fa-credit-card',
                    'fingerprint_capture': 'fas fa-fingerprint',
                    'mode_change': 'fas fa-exchange-alt',
                    'api_call': 'fas fa-globe',
                    'error': 'fas fa-exclamation-triangle',
                    'heartbeat': 'fas fa-heartbeat'
                };
                
                const colorMap = {
                    'rfid_scan': 'var(--gradient-primary)',
                    'fingerprint_capture': 'var(--gradient-success)',
                    'mode_change': 'var(--gradient-primary)',
                    'api_call': 'var(--gradient-info)',
                    'error': 'var(--gradient-error)',
                    'heartbeat': 'var(--gradient-success)'
                };
                
                const logTime = new Date(log.created_at).toLocaleString();
                
                logItem.innerHTML = `
                    <div class="activity-icon" style="background: ${colorMap[log.event_type] || 'var(--gradient-primary)'};">
                        <i class="${iconMap[log.event_type] || 'fas fa-info'}"></i>
                    </div>
                    <div class="activity-content">
                        <div class="activity-text">${log.event_type.replace('_', ' ').toUpperCase()}: ${eventData.description || eventData.rfid_id || eventData.fingerprint_id || 'System event'}</div>
                        <div class="activity-time">${logTime}</div>
                    </div>
                `;
                
                systemLogsContainer.appendChild(logItem);
            });
        }

        // ==========================================
        // Utility Functions
        // ==========================================
        function setTodayDate() {
            const today = new Date().toISOString().split('T')[0];
            const examDateInput = document.getElementById('exam-date');
            if (examDateInput) {
                examDateInput.value = today;
            }
        }

        // ==========================================
        // Window Resize Handler
        // ==========================================
        window.addEventListener('resize', function() {
            if (window.innerWidth > 768) {
                const sidebar = document.getElementById('sidebar');
                const mainContent = document.getElementById('main-content');
                
                sidebar.classList.remove('mobile-visible');
                mainContent.classList.remove('sidebar-collapsed');
            }
        });

        // ==========================================
        // Keyboard Shortcuts
        // ==========================================
        document.addEventListener('keydown', function(e) {
            // Ctrl/Cmd + Number keys for quick panel switching
            if ((e.ctrlKey || e.metaKey) && !e.shiftKey && !e.altKey) {
                const panels = ['registration', 'attendance', 'finance', 'exam', 'system'];
                const num = parseInt(e.key);
                
                if (num >= 1 && num <= 5) {
                    e.preventDefault();
                    const navLink = document.querySelector(`.nav-link[onclick*="${panels[num-1]}"]`);
                    if (navLink) {
                        showPanel(panels[num-1], navLink);
                    }
                }
            }
            
            // ESC key to close mobile sidebar
            if (e.key === 'Escape') {
                const sidebar = document.getElementById('sidebar');
                if (sidebar.classList.contains('mobile-visible')) {
                    toggleSidebar();
                }
            }
        });

        // ==========================================
        // Error Handler
        // ==========================================
        window.addEventListener('error', function(e) {
            console.error('JavaScript Error:', e.error);
            showNotification('An unexpected error occurred. Please refresh the page.', 'error');
        });

        // ==========================================
        // Unload Handler
        // ==========================================
        window.addEventListener('beforeunload', function() {
            stopESP32Monitoring();
        });

        console.log("🎉 Advanced Attendance System v2.3 - Complete Enhanced Frontend Loaded Successfully!");
    </script>
</body>
</html>
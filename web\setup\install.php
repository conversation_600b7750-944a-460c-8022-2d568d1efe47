<?php
/**
 * Installation Script for Advanced Attendance System
 * Run this once to set up the database and initial configuration
 */

// Check if already installed
if (file_exists(__DIR__ . '/../config/.installed')) {
    die('System is already installed. Delete config/.installed file to reinstall.');
}

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $host = $_POST['host'] ?? 'localhost';
    $username = $_POST['username'] ?? 'root';
    $password = $_POST['password'] ?? '';
    $database = $_POST['database'] ?? 'attendance_system';
    
    try {
        // Test connection
        $pdo = new PDO("mysql:host=$host", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Create database
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `$database`");
        $pdo->exec("USE `$database`");
        
        // Read and execute SQL file
        $sql = file_get_contents(__DIR__ . '/database.sql');
        $statements = explode(';', $sql);
        
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement)) {
                $pdo->exec($statement);
            }
        }
        
        // Update database config
        $configContent = "<?php
/**
 * Database Configuration for Advanced Attendance System
 */

class Database {
    private \$host = '$host';
    private \$db_name = '$database';
    private \$username = '$username';
    private \$password = '$password';
    private \$conn;

    public function getConnection() {
        \$this->conn = null;
        
        try {
            \$this->conn = new PDO(
                \"mysql:host=\" . \$this->host . \";dbname=\" . \$this->db_name,
                \$this->username,
                \$this->password,
                array(
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::MYSQL_ATTR_INIT_COMMAND => \"SET NAMES utf8\"
                )
            );
        } catch(PDOException \$exception) {
            error_log(\"Connection error: \" . \$exception->getMessage());
            throw new Exception(\"Database connection failed\");
        }
        
        return \$this->conn;
    }
}

/**
 * Global database connection function
 */
function getDB() {
    static \$database = null;
    if (\$database === null) {
        \$database = new Database();
    }
    return \$database->getConnection();
}
?>";
        
        file_put_contents(__DIR__ . '/../config/database.php', $configContent);
        
        // Create installation marker
        if (!is_dir(__DIR__ . '/../config')) {
            mkdir(__DIR__ . '/../config', 0755, true);
        }
        file_put_contents(__DIR__ . '/../config/.installed', date('Y-m-d H:i:s'));
        
        $success = 'Installation completed successfully! You can now login with:<br>
                   <strong>Username:</strong> admin<br>
                   <strong>Password:</strong> admin123<br><br>
                   <a href="../auth/login.php" class="btn btn-primary">Go to Login</a>';
        
    } catch (Exception $e) {
        $error = 'Installation failed: ' . $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Install - Advanced Attendance System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        
        .install-card {
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            padding: 3rem;
            width: 100%;
            max-width: 500px;
        }
        
        .install-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .install-icon {
            width: 64px;
            height: 64px;
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            color: white;
            font-size: 1.5rem;
        }
    </style>
</head>
<body>
    <div class="install-card">
        <div class="install-header">
            <div class="install-icon">
                <i class="fas fa-graduation-cap"></i>
            </div>
            <h1 class="h3 mb-2">Advanced Attendance System</h1>
            <p class="text-muted">Database Installation</p>
        </div>
        
        <?php if ($error): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle me-2"></i>
                <?php echo $error; ?>
            </div>
        <?php endif; ?>
        
        <?php if ($success): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo $success; ?>
            </div>
        <?php else: ?>
            <form method="POST">
                <div class="mb-3">
                    <label for="host" class="form-label">Database Host</label>
                    <input type="text" class="form-control" id="host" name="host" value="localhost" required>
                </div>
                
                <div class="mb-3">
                    <label for="database" class="form-label">Database Name</label>
                    <input type="text" class="form-control" id="database" name="database" value="attendance_system" required>
                </div>
                
                <div class="mb-3">
                    <label for="username" class="form-label">Database Username</label>
                    <input type="text" class="form-control" id="username" name="username" value="root" required>
                </div>
                
                <div class="mb-4">
                    <label for="password" class="form-label">Database Password</label>
                    <input type="password" class="form-control" id="password" name="password">
                    <div class="form-text">Leave empty if no password is set</div>
                </div>
                
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-download me-2"></i>
                    Install Database
                </button>
            </form>
            
            <div class="mt-4 p-3 bg-light rounded">
                <h6 class="mb-2">Installation Notes:</h6>
                <ul class="small mb-0">
                    <li>Make sure MySQL/MariaDB is running</li>
                    <li>Ensure the database user has CREATE privileges</li>
                    <li>Default admin credentials will be: admin/admin123</li>
                </ul>
            </div>
        <?php endif; ?>
    </div>
</body>
</html>

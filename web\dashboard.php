<?php
$pageTitle = 'Dashboard';
require_once __DIR__ . '/includes/header.php';

// Get dashboard statistics
try {
    $db = getDB();
    
    // Total students
    $stmt = $db->query("SELECT COUNT(*) as total FROM students WHERE status = 'active'");
    $totalStudents = $stmt->fetch()['total'];
    
    // Present today
    $stmt = $db->query("SELECT COUNT(DISTINCT student_id) as present FROM attendance WHERE DATE(date) = CURDATE() AND status = 'present'");
    $presentToday = $stmt->fetch()['present'];
    
    // Total payments this month
    $stmt = $db->query("SELECT COALESCE(SUM(amount), 0) as total FROM payments WHERE MONTH(payment_date) = MONTH(CURDATE()) AND YEAR(payment_date) = YEAR(CURDATE())");
    $monthlyPayments = $stmt->fetch()['total'];
    
    // Pending payments
    $stmt = $db->query("SELECT COUNT(*) as pending FROM students s LEFT JOIN payments p ON s.id = p.student_id AND MONTH(p.payment_date) = MONTH(CURDATE()) WHERE s.status = 'active' AND p.id IS NULL");
    $pendingPayments = $stmt->fetch()['pending'];
    
    // Recent attendance
    $stmt = $db->query("
        SELECT s.name, s.student_id, a.status, a.date, a.time_in 
        FROM attendance a 
        JOIN students s ON a.student_id = s.id 
        WHERE DATE(a.date) = CURDATE() 
        ORDER BY a.time_in DESC 
        LIMIT 10
    ");
    $recentAttendance = $stmt->fetchAll();
    
    // Recent payments
    $stmt = $db->query("
        SELECT s.name, s.student_id, p.amount, p.payment_date, p.payment_type 
        FROM payments p 
        JOIN students s ON p.student_id = s.id 
        ORDER BY p.payment_date DESC 
        LIMIT 10
    ");
    $recentPayments = $stmt->fetchAll();
    
} catch (Exception $e) {
    error_log("Dashboard error: " . $e->getMessage());
    $totalStudents = $presentToday = $monthlyPayments = $pendingPayments = 0;
    $recentAttendance = $recentPayments = [];
}

$attendanceRate = $totalStudents > 0 ? round(($presentToday / $totalStudents) * 100, 1) : 0;
?>

<!-- Dashboard Stats -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="d-flex align-items-center">
                <div class="stats-icon" style="background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);">
                    <i class="fas fa-users"></i>
                </div>
                <div class="ms-3">
                    <div class="text-muted small">Total Students</div>
                    <div class="h4 mb-0"><?php echo number_format($totalStudents); ?></div>
                    <div class="small" style="color: var(--primary-color);">Registered</div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="d-flex align-items-center">
                <div class="stats-icon" style="background: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-light) 100%);">
                    <i class="fas fa-user-check"></i>
                </div>
                <div class="ms-3">
                    <div class="text-muted small">Present Today</div>
                    <div class="h4 mb-0"><?php echo number_format($presentToday); ?></div>
                    <div class="small" style="color: var(--secondary-color);"><?php echo $attendanceRate; ?>% attendance</div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="d-flex align-items-center">
                <div class="stats-icon" style="background: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-light) 100%);">
                    <i class="fas fa-money-bill-wave"></i>
                </div>
                <div class="ms-3">
                    <div class="text-muted small">Monthly Revenue</div>
                    <div class="h4 mb-0">$<?php echo number_format($monthlyPayments, 2); ?></div>
                    <div class="small" style="color: var(--accent-color);">This month</div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="d-flex align-items-center">
                <div class="stats-icon" style="background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="ms-3">
                    <div class="text-muted small">Pending Payments</div>
                    <div class="h4 mb-0"><?php echo number_format($pendingPayments); ?></div>
                    <div class="small text-danger">Requires attention</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row mb-4">
    <div class="col-xl-8 mb-4">
        <div class="table-card">
            <div class="table-header">
                <h5 class="mb-0">Attendance Overview</h5>
            </div>
            <div class="p-3">
                <canvas id="attendanceChart" height="100"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-xl-4 mb-4">
        <div class="table-card">
            <div class="table-header">
                <h5 class="mb-0">Payment Status</h5>
            </div>
            <div class="p-3">
                <canvas id="paymentChart" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row">
    <div class="col-xl-6 mb-4">
        <div class="table-card">
            <div class="table-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Recent Attendance</h5>
                <div class="d-flex gap-2">
                    <a href="<?php echo BASE_URL; ?>attendance/index.php" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-eye"></i>
                        View All
                    </a>
                    <button class="btn btn-sm btn-fresh">
                        <i class="fas fa-sync"></i>
                        Refresh
                    </button>
                </div>
            </div>
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>Student</th>
                            <th>Status</th>
                            <th>Time</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($recentAttendance)): ?>
                            <tr>
                                <td colspan="3" class="text-center text-muted py-4">No attendance records today</td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($recentAttendance as $record): ?>
                                <tr>
                                    <td>
                                        <div class="fw-medium"><?php echo htmlspecialchars($record['name']); ?></div>
                                        <div class="small text-muted"><?php echo htmlspecialchars($record['student_id']); ?></div>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php echo $record['status'] == 'present' ? 'success' : 'danger'; ?>">
                                            <?php echo ucfirst($record['status']); ?>
                                        </span>
                                    </td>
                                    <td class="text-muted">
                                        <?php echo $record['time_in'] ? date('H:i', strtotime($record['time_in'])) : '-'; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <div class="col-xl-6 mb-4">
        <div class="table-card">
            <div class="table-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Recent Payments</h5>
                <div class="d-flex gap-2">
                    <a href="<?php echo BASE_URL; ?>finance/index.php" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-eye"></i>
                        View All
                    </a>
                    <button class="btn btn-sm btn-add">
                        <i class="fas fa-plus"></i>
                        Add Payment
                    </button>
                </div>
            </div>
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>Student</th>
                            <th>Amount</th>
                            <th>Date</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($recentPayments)): ?>
                            <tr>
                                <td colspan="3" class="text-center text-muted py-4">No recent payments</td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($recentPayments as $payment): ?>
                                <tr>
                                    <td>
                                        <div class="fw-medium"><?php echo htmlspecialchars($payment['name']); ?></div>
                                        <div class="small text-muted"><?php echo htmlspecialchars($payment['student_id']); ?></div>
                                    </td>
                                    <td class="fw-medium text-success">
                                        $<?php echo number_format($payment['amount'], 2); ?>
                                    </td>
                                    <td class="text-muted">
                                        <?php echo date('M j', strtotime($payment['payment_date'])); ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<?php
$additionalJS = "
<script>
// Attendance Chart
const attendanceCtx = document.getElementById('attendanceChart').getContext('2d');
new Chart(attendanceCtx, {
    type: 'line',
    data: {
        labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
        datasets: [{
            label: 'Present',
            data: [85, 92, 78, 88, 95, 67, 45],
            borderColor: '#10b981',
            backgroundColor: 'rgba(16, 185, 129, 0.1)',
            tension: 0.4
        }, {
            label: 'Absent',
            data: [15, 8, 22, 12, 5, 33, 55],
            borderColor: '#ef4444',
            backgroundColor: 'rgba(239, 68, 68, 0.1)',
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'top',
            }
        },
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Payment Chart
const paymentCtx = document.getElementById('paymentChart').getContext('2d');
new Chart(paymentCtx, {
    type: 'doughnut',
    data: {
        labels: ['Paid', 'Pending'],
        datasets: [{
            data: [" . ($totalStudents - $pendingPayments) . ", " . $pendingPayments . "],
            backgroundColor: ['#10b981', '#ef4444']
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
            }
        }
    }
});
</script>
";

require_once __DIR__ . '/includes/footer.php';
?>

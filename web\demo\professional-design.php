<?php
$pageTitle = 'Professional Design Demo';
require_once __DIR__ . '/../includes/header.php';
?>

<div class="page-header">
    <h1 class="page-title">Professional Design Showcase</h1>
    <p class="text-muted">Modern, clean, and professional UI components</p>
</div>

<!-- Professional Stats Cards -->
<div class="row mb-5">
    <div class="col-12">
        <h3 class="mb-4">Professional Stats Cards</h3>
    </div>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="d-flex align-items-center">
                <div class="stats-icon" style="background: linear-gradient(135deg, var(--color-primary) 0%, var(--primary-light) 100%);">
                    <i class="fas fa-users"></i>
                </div>
                <div class="ms-3">
                    <div class="text-muted small">Total Students</div>
                    <div class="h4 mb-0">1,247</div>
                    <div class="small" style="color: var(--color-primary);">+12% from last month</div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="d-flex align-items-center">
                <div class="stats-icon" style="background: linear-gradient(135deg, var(--color-secondary) 0%, var(--secondary-light) 100%);">
                    <i class="fas fa-user-check"></i>
                </div>
                <div class="ms-3">
                    <div class="text-muted small">Present Today</div>
                    <div class="h4 mb-0">1,089</div>
                    <div class="small" style="color: var(--color-secondary);">87.3% attendance</div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="d-flex align-items-center">
                <div class="stats-icon" style="background: linear-gradient(135deg, var(--color-accent) 0%, var(--accent-light) 100%);">
                    <i class="fas fa-money-bill-wave"></i>
                </div>
                <div class="ms-3">
                    <div class="text-muted small">Monthly Revenue</div>
                    <div class="h4 mb-0">$24,890</div>
                    <div class="small" style="color: var(--color-accent);">+8% from last month</div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="d-flex align-items-center">
                <div class="stats-icon" style="background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="ms-3">
                    <div class="text-muted small">Pending Issues</div>
                    <div class="h4 mb-0">23</div>
                    <div class="small text-danger">Requires attention</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Professional Buttons -->
<div class="row mb-5">
    <div class="col-12">
        <h3 class="mb-4">Professional Button Styles</h3>
    </div>
    <div class="col-md-6">
        <div class="table-card">
            <div class="table-header">
                <h5 class="mb-0">Primary Actions</h5>
            </div>
            <div class="p-4">
                <div class="d-flex flex-wrap gap-3 mb-3">
                    <button class="btn btn-add">
                        <i class="fas fa-plus"></i>
                        Add Student
                    </button>
                    <button class="btn btn-fresh">
                        <i class="fas fa-sync"></i>
                        Refresh Data
                    </button>
                    <button class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        Save Changes
                    </button>
                </div>
                <div class="d-flex flex-wrap gap-3">
                    <button class="btn btn-success">
                        <i class="fas fa-check"></i>
                        Approve
                    </button>
                    <button class="btn btn-delete">
                        <i class="fas fa-trash"></i>
                        Delete
                    </button>
                    <button class="btn btn-clear">
                        <i class="fas fa-times"></i>
                        Cancel
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="table-card">
            <div class="table-header">
                <h5 class="mb-0">Outline Buttons</h5>
            </div>
            <div class="p-4">
                <div class="d-flex flex-wrap gap-3 mb-3">
                    <button class="btn btn-outline-primary">
                        <i class="fas fa-edit"></i>
                        Edit
                    </button>
                    <button class="btn btn-outline-secondary">
                        <i class="fas fa-download"></i>
                        Export
                    </button>
                    <button class="btn btn-outline-danger">
                        <i class="fas fa-ban"></i>
                        Block
                    </button>
                </div>
                <div class="d-flex flex-wrap gap-3">
                    <button class="btn btn-sm btn-primary">
                        <i class="fas fa-eye"></i>
                        View
                    </button>
                    <button class="btn btn-lg btn-success">
                        <i class="fas fa-rocket"></i>
                        Launch
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Professional Table -->
<div class="row mb-5">
    <div class="col-12">
        <h3 class="mb-4">Professional Table Design</h3>
        <div class="table-card">
            <div class="table-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Student Management</h5>
                <div class="d-flex gap-2">
                    <button class="btn btn-sm btn-add">
                        <i class="fas fa-plus"></i>
                        Add New
                    </button>
                    <button class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-filter"></i>
                        Filter
                    </button>
                </div>
            </div>
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>Student ID</th>
                            <th>Name</th>
                            <th>Status</th>
                            <th>Last Attendance</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="fw-medium">#STU001</td>
                            <td>
                                <div class="fw-medium">John Doe</div>
                                <div class="small text-muted"><EMAIL></div>
                            </td>
                            <td>
                                <span class="badge" style="background: rgba(16, 185, 129, 0.1); color: var(--color-secondary);">Active</span>
                            </td>
                            <td class="text-muted">2 hours ago</td>
                            <td>
                                <div class="d-flex gap-1">
                                    <button class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-secondary">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="fw-medium">#STU002</td>
                            <td>
                                <div class="fw-medium">Jane Smith</div>
                                <div class="small text-muted"><EMAIL></div>
                            </td>
                            <td>
                                <span class="badge" style="background: rgba(245, 158, 11, 0.1); color: var(--color-accent);">Pending</span>
                            </td>
                            <td class="text-muted">1 day ago</td>
                            <td>
                                <div class="d-flex gap-1">
                                    <button class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-secondary">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="fw-medium">#STU003</td>
                            <td>
                                <div class="fw-medium">Mike Johnson</div>
                                <div class="small text-muted"><EMAIL></div>
                            </td>
                            <td>
                                <span class="badge" style="background: rgba(239, 68, 68, 0.1); color: #ef4444;">Inactive</span>
                            </td>
                            <td class="text-muted">1 week ago</td>
                            <td>
                                <div class="d-flex gap-1">
                                    <button class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-secondary">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Design Guidelines -->
<div class="row">
    <div class="col-12">
        <div class="table-card">
            <div class="table-header">
                <h5 class="mb-0">Professional Design Guidelines</h5>
            </div>
            <div class="p-4">
                <div class="row">
                    <div class="col-md-4">
                        <h6 class="fw-bold mb-3">🎨 Color Consistency</h6>
                        <ul class="list-unstyled">
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Three main colors only</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Consistent usage patterns</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Proper contrast ratios</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Semantic color meanings</li>
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <h6 class="fw-bold mb-3">📐 Layout & Spacing</h6>
                        <ul class="list-unstyled">
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Consistent padding/margins</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Proper visual hierarchy</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Clean white space usage</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Responsive grid system</li>
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <h6 class="fw-bold mb-3">✨ Interactive Elements</h6>
                        <ul class="list-unstyled">
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Smooth hover transitions</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Clear focus states</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Subtle shadow effects</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Professional animations</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/../includes/footer.php'; ?>

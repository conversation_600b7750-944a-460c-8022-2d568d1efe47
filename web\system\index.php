<?php
$pageTitle = 'System Management';
require_once __DIR__ . '/../includes/header.php';

// Handle system actions
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $db = getDB();
        
        if (isset($_POST['clear_logs'])) {
            // Clear system logs (implement based on your logging system)
            setFlashMessage('success', 'System logs cleared successfully!');
        } elseif (isset($_POST['backup_database'])) {
            // Backup database (implement based on your backup system)
            setFlashMessage('success', 'Database backup initiated!');
        } elseif (isset($_POST['reset_system'])) {
            // Reset system settings (implement based on your requirements)
            setFlashMessage('warning', 'System reset completed!');
        }
        
        header('Location: ' . $_SERVER['REQUEST_URI']);
        exit();
        
    } catch (Exception $e) {
        error_log("System action error: " . $e->getMessage());
        setFlashMessage('danger', 'Error performing system action. Please try again.');
    }
}

try {
    $db = getDB();
    
    // Get system statistics
    $statsStmt = $db->query("
        SELECT 
            (SELECT COUNT(*) FROM students) as total_students,
            (SELECT COUNT(*) FROM attendance WHERE DATE(date) = CURDATE()) as today_attendance,
            (SELECT COUNT(*) FROM payments WHERE MONTH(payment_date) = MONTH(CURDATE())) as monthly_payments,
            (SELECT COUNT(*) FROM exams WHERE exam_date >= CURDATE()) as upcoming_exams
    ");
    $stats = $statsStmt->fetch();
    
    // Get recent system activities (you can implement a proper activity log table)
    $activities = [
        ['action' => 'User Login', 'user' => 'Admin', 'time' => date('Y-m-d H:i:s'), 'status' => 'success'],
        ['action' => 'Database Backup', 'user' => 'System', 'time' => date('Y-m-d H:i:s', strtotime('-1 hour')), 'status' => 'success'],
        ['action' => 'Student Registration', 'user' => 'Admin', 'time' => date('Y-m-d H:i:s', strtotime('-2 hours')), 'status' => 'success'],
        ['action' => 'Payment Processing', 'user' => 'Admin', 'time' => date('Y-m-d H:i:s', strtotime('-3 hours')), 'status' => 'success'],
    ];
    
} catch (Exception $e) {
    error_log("System page error: " . $e->getMessage());
    $stats = ['total_students' => 0, 'today_attendance' => 0, 'monthly_payments' => 0, 'upcoming_exams' => 0];
    $activities = [];
}
?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="page-title mb-0">System Management</h1>
        <p class="text-muted">Monitor system health and manage configurations</p>
    </div>
    <div>
        <button class="btn btn-outline-primary me-2">
            <i class="fas fa-download me-2"></i>Export Logs
        </button>
        <button class="btn btn-fresh">
            <i class="fas fa-sync me-2"></i>Refresh Status
        </button>
    </div>
</div>

<!-- System Overview -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="d-flex align-items-center">
                <div class="stats-icon" style="background: linear-gradient(135deg, var(--color-primary) 0%, var(--primary-light) 100%);">
                    <i class="fas fa-database"></i>
                </div>
                <div class="ms-3">
                    <div class="text-muted small">Database Records</div>
                    <div class="h4 mb-0"><?php echo number_format($stats['total_students']); ?></div>
                    <div class="small" style="color: var(--color-primary);">Students</div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="d-flex align-items-center">
                <div class="stats-icon" style="background: linear-gradient(135deg, var(--color-secondary) 0%, var(--secondary-light) 100%);">
                    <i class="fas fa-heartbeat"></i>
                </div>
                <div class="ms-3">
                    <div class="text-muted small">System Status</div>
                    <div class="h4 mb-0">Online</div>
                    <div class="small" style="color: var(--color-secondary);">All services running</div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="d-flex align-items-center">
                <div class="stats-icon" style="background: linear-gradient(135deg, var(--color-accent) 0%, var(--accent-light) 100%);">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="ms-3">
                    <div class="text-muted small">Uptime</div>
                    <div class="h4 mb-0">99.9%</div>
                    <div class="small" style="color: var(--color-accent);">Last 30 days</div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="d-flex align-items-center">
                <div class="stats-icon" style="background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <div class="ms-3">
                    <div class="text-muted small">Security</div>
                    <div class="h4 mb-0">Secure</div>
                    <div class="small text-muted">No threats detected</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- System Actions -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="table-card">
            <div class="table-header">
                <h5 class="mb-0">System Actions</h5>
            </div>
            <div class="p-4">
                <div class="d-grid gap-3">
                    <form method="POST" style="display: inline;">
                        <button type="submit" name="backup_database" class="btn btn-add w-100">
                            <i class="fas fa-download me-2"></i>Backup Database
                        </button>
                    </form>
                    
                    <form method="POST" style="display: inline;">
                        <button type="submit" name="clear_logs" class="btn btn-outline-warning w-100">
                            <i class="fas fa-broom me-2"></i>Clear System Logs
                        </button>
                    </form>
                    
                    <button class="btn btn-outline-primary w-100">
                        <i class="fas fa-cog me-2"></i>System Settings
                    </button>
                    
                    <form method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to reset the system? This action cannot be undone.')">
                        <button type="submit" name="reset_system" class="btn btn-outline-danger w-100">
                            <i class="fas fa-exclamation-triangle me-2"></i>Reset System
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="table-card">
            <div class="table-header">
                <h5 class="mb-0">Hardware Status</h5>
            </div>
            <div class="p-4">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div>
                        <div class="fw-medium">ESP32 Controller</div>
                        <div class="small text-muted">Main hardware interface</div>
                    </div>
                    <span class="badge bg-success">Online</span>
                </div>
                
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div>
                        <div class="fw-medium">RFID Scanner</div>
                        <div class="small text-muted">Card reader module</div>
                    </div>
                    <span class="badge bg-success">Active</span>
                </div>
                
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div>
                        <div class="fw-medium">Fingerprint Scanner</div>
                        <div class="small text-muted">Biometric sensor</div>
                    </div>
                    <span class="badge bg-success">Ready</span>
                </div>
                
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="fw-medium">Database Connection</div>
                        <div class="small text-muted">MySQL server</div>
                    </div>
                    <span class="badge bg-success">Connected</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activities -->
<div class="table-card">
    <div class="table-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Recent System Activities</h5>
        <div class="d-flex gap-2">
            <button class="btn btn-sm btn-outline-primary">
                <i class="fas fa-filter"></i>
                Filter
            </button>
            <button class="btn btn-sm btn-fresh">
                <i class="fas fa-sync"></i>
                Refresh
            </button>
        </div>
    </div>
    <div class="table-responsive">
        <table class="table table-hover mb-0">
            <thead class="table-light">
                <tr>
                    <th>Action</th>
                    <th>User</th>
                    <th>Time</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($activities)): ?>
                    <tr>
                        <td colspan="4" class="text-center text-muted py-4">No recent activities</td>
                    </tr>
                <?php else: ?>
                    <?php foreach ($activities as $activity): ?>
                        <tr>
                            <td class="fw-medium"><?php echo htmlspecialchars($activity['action']); ?></td>
                            <td><?php echo htmlspecialchars($activity['user']); ?></td>
                            <td><?php echo date('M j, Y g:i A', strtotime($activity['time'])); ?></td>
                            <td>
                                <span class="badge bg-<?php echo $activity['status'] == 'success' ? 'success' : 'danger'; ?>">
                                    <?php echo ucfirst($activity['status']); ?>
                                </span>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>

<?php require_once __DIR__ . '/../includes/footer.php'; ?>

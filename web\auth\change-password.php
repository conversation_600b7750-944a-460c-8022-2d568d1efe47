<?php
$pageTitle = 'Change Password';
require_once __DIR__ . '/../includes/header.php';

$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $currentPassword = $_POST['current_password'] ?? '';
    $newPassword = $_POST['new_password'] ?? '';
    $confirmPassword = $_POST['confirm_password'] ?? '';
    
    if (empty($currentPassword) || empty($newPassword) || empty($confirmPassword)) {
        $message = 'Please fill in all fields.';
        $messageType = 'danger';
    } elseif (strlen($newPassword) < 6) {
        $message = 'New password must be at least 6 characters long.';
        $messageType = 'danger';
    } elseif ($newPassword !== $confirmPassword) {
        $message = 'New passwords do not match.';
        $messageType = 'danger';
    } else {
        try {
            $db = getDB();
            
            // Verify current password
            $stmt = $db->prepare("SELECT password FROM users WHERE id = ?");
            $stmt->execute([$_SESSION['user_id']]);
            $user = $stmt->fetch();
            
            if (!$user || !password_verify($currentPassword, $user['password'])) {
                $message = 'Current password is incorrect.';
                $messageType = 'danger';
            } else {
                // Update password
                $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
                $stmt = $db->prepare("UPDATE users SET password = ?, updated_at = NOW() WHERE id = ?");
                $stmt->execute([$hashedPassword, $_SESSION['user_id']]);
                
                $message = 'Password changed successfully!';
                $messageType = 'success';
            }
            
        } catch (Exception $e) {
            error_log("Password change error: " . $e->getMessage());
            $message = 'An error occurred. Please try again.';
            $messageType = 'danger';
        }
    }
}
?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="page-title mb-0">Change Password</h1>
        <p class="text-muted">Update your account password</p>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="table-card">
            <div class="table-header">
                <h5 class="mb-0">
                    <i class="fas fa-lock me-2"></i>
                    Change Password
                </h5>
            </div>
            
            <div class="p-4">
                <?php if ($message): ?>
                    <div class="alert alert-<?php echo $messageType; ?>" role="alert">
                        <i class="fas fa-<?php echo $messageType === 'success' ? 'check-circle' : 'exclamation-circle'; ?> me-2"></i>
                        <?php echo htmlspecialchars($message); ?>
                    </div>
                <?php endif; ?>
                
                <form method="POST" action="" id="changePasswordForm">
                    <div class="mb-3">
                        <label for="current_password" class="form-label">Current Password</label>
                        <input type="password" class="form-control" id="current_password" name="current_password" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="new_password" class="form-label">New Password</label>
                        <input type="password" class="form-control" id="new_password" name="new_password" required minlength="6">
                        <div class="password-strength" id="passwordStrength"></div>
                    </div>
                    
                    <div class="mb-4">
                        <label for="confirm_password" class="form-label">Confirm New Password</label>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                        <div class="invalid-feedback" id="passwordMatch"></div>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-add" id="submitBtn">
                            <i class="fas fa-save me-2"></i>
                            Change Password
                        </button>
                        <a href="<?php echo BASE_URL; ?>dashboard.php" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>
                            Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Security Tips -->
        <div class="table-card mt-4">
            <div class="table-header">
                <h6 class="mb-0">
                    <i class="fas fa-shield-alt me-2"></i>
                    Password Security Tips
                </h6>
            </div>
            <div class="p-4">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        Use at least 8 characters
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        Include uppercase and lowercase letters
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        Add numbers and special characters
                    </li>
                    <li class="mb-0">
                        <i class="fas fa-check text-success me-2"></i>
                        Avoid common words or personal information
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

<style>
.password-strength {
    font-size: 0.75rem;
    margin-top: 0.25rem;
}

.strength-weak { color: #dc3545; }
.strength-medium { color: #ffc107; }
.strength-strong { color: #28a745; }
</style>

<script>
// Password strength checker
document.getElementById('new_password').addEventListener('input', function() {
    const password = this.value;
    const strengthDiv = document.getElementById('passwordStrength');
    
    if (password.length === 0) {
        strengthDiv.textContent = '';
        return;
    }
    
    let strength = 0;
    if (password.length >= 6) strength++;
    if (password.match(/[a-z]/)) strength++;
    if (password.match(/[A-Z]/)) strength++;
    if (password.match(/[0-9]/)) strength++;
    if (password.match(/[^a-zA-Z0-9]/)) strength++;
    
    if (strength < 2) {
        strengthDiv.textContent = 'Weak password';
        strengthDiv.className = 'password-strength strength-weak';
    } else if (strength < 4) {
        strengthDiv.textContent = 'Medium strength';
        strengthDiv.className = 'password-strength strength-medium';
    } else {
        strengthDiv.textContent = 'Strong password';
        strengthDiv.className = 'password-strength strength-strong';
    }
});

// Password match checker
document.getElementById('confirm_password').addEventListener('input', function() {
    const password = document.getElementById('new_password').value;
    const confirmPassword = this.value;
    const matchDiv = document.getElementById('passwordMatch');
    const submitBtn = document.getElementById('submitBtn');
    
    if (confirmPassword.length === 0) {
        matchDiv.textContent = '';
        this.classList.remove('is-invalid', 'is-valid');
        return;
    }
    
    if (password === confirmPassword) {
        matchDiv.textContent = '';
        this.classList.remove('is-invalid');
        this.classList.add('is-valid');
        submitBtn.disabled = false;
    } else {
        matchDiv.textContent = 'Passwords do not match';
        this.classList.remove('is-valid');
        this.classList.add('is-invalid');
        submitBtn.disabled = true;
    }
});
</script>

<?php require_once __DIR__ . '/../includes/footer.php'; ?>

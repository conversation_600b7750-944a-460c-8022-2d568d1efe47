<?php
$pageTitle = 'Examination Management';
require_once __DIR__ . '/../includes/header.php';

// Handle exam submission
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['add_exam'])) {
    try {
        $db = getDB();
        
        $stmt = $db->prepare("
            INSERT INTO exams (title, description, exam_date, duration, total_marks, created_by) 
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            $_POST['title'],
            $_POST['description'],
            $_POST['exam_date'],
            $_POST['duration'],
            $_POST['total_marks'],
            $_SESSION['user_id']
        ]);
        
        setFlashMessage('success', 'Exam added successfully!');
        header('Location: ' . $_SERVER['REQUEST_URI']);
        exit();
        
    } catch (Exception $e) {
        error_log("Exam error: " . $e->getMessage());
        setFlashMessage('danger', 'Error adding exam. Please try again.');
    }
}

try {
    $db = getDB();
    
    // Get exam statistics
    $statsStmt = $db->query("
        SELECT 
            COUNT(*) as total_exams,
            COUNT(CASE WHEN exam_date >= CURDATE() THEN 1 END) as upcoming_exams,
            COUNT(CASE WHEN exam_date < CURDATE() THEN 1 END) as completed_exams,
            COUNT(CASE WHEN DATE(exam_date) = CURDATE() THEN 1 END) as today_exams
        FROM exams
    ");
    $stats = $statsStmt->fetch();
    
    // Get recent exams
    $examsStmt = $db->query("
        SELECT * FROM exams 
        ORDER BY exam_date DESC, created_at DESC 
        LIMIT 50
    ");
    $exams = $examsStmt->fetchAll();
    
} catch (Exception $e) {
    error_log("Examination page error: " . $e->getMessage());
    $stats = ['total_exams' => 0, 'upcoming_exams' => 0, 'completed_exams' => 0, 'today_exams' => 0];
    $exams = [];
}
?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="page-title mb-0">Examination Management</h1>
        <p class="text-muted">Manage exams, schedules, and results</p>
    </div>
    <div>
        <button class="btn btn-outline-primary me-2">
            <i class="fas fa-chart-bar me-2"></i>Reports
        </button>
        <button class="btn btn-add" data-bs-toggle="modal" data-bs-target="#addExamModal">
            <i class="fas fa-plus me-2"></i>Add Exam
        </button>
    </div>
</div>

<!-- Examination Statistics -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="d-flex align-items-center">
                <div class="stats-icon" style="background: linear-gradient(135deg, var(--color-primary) 0%, var(--primary-light) 100%);">
                    <i class="fas fa-clipboard-list"></i>
                </div>
                <div class="ms-3">
                    <div class="text-muted small">Total Exams</div>
                    <div class="h4 mb-0"><?php echo number_format($stats['total_exams']); ?></div>
                    <div class="small" style="color: var(--color-primary);">All time</div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="d-flex align-items-center">
                <div class="stats-icon" style="background: linear-gradient(135deg, var(--color-secondary) 0%, var(--secondary-light) 100%);">
                    <i class="fas fa-calendar-plus"></i>
                </div>
                <div class="ms-3">
                    <div class="text-muted small">Upcoming Exams</div>
                    <div class="h4 mb-0"><?php echo number_format($stats['upcoming_exams']); ?></div>
                    <div class="small" style="color: var(--color-secondary);">Scheduled</div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="d-flex align-items-center">
                <div class="stats-icon" style="background: linear-gradient(135deg, var(--color-accent) 0%, var(--accent-light) 100%);">
                    <i class="fas fa-calendar-day"></i>
                </div>
                <div class="ms-3">
                    <div class="text-muted small">Today's Exams</div>
                    <div class="h4 mb-0"><?php echo number_format($stats['today_exams']); ?></div>
                    <div class="small" style="color: var(--color-accent);">In progress</div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="d-flex align-items-center">
                <div class="stats-icon" style="background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="ms-3">
                    <div class="text-muted small">Completed</div>
                    <div class="h4 mb-0"><?php echo number_format($stats['completed_exams']); ?></div>
                    <div class="small text-muted">Finished</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Exams Table -->
<div class="table-card">
    <div class="table-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Examination Schedule</h5>
        <div class="d-flex gap-2">
            <button class="btn btn-sm btn-outline-primary">
                <i class="fas fa-filter"></i>
                Filter
            </button>
            <button class="btn btn-sm btn-fresh">
                <i class="fas fa-sync"></i>
                Refresh
            </button>
        </div>
    </div>
    <div class="table-responsive">
        <table class="table table-hover mb-0">
            <thead class="table-light">
                <tr>
                    <th>Exam Title</th>
                    <th>Date & Time</th>
                    <th>Duration</th>
                    <th>Total Marks</th>
                    <th>Status</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($exams)): ?>
                    <tr>
                        <td colspan="6" class="text-center text-muted py-4">No exams scheduled</td>
                    </tr>
                <?php else: ?>
                    <?php foreach ($exams as $exam): ?>
                        <?php
                        $examDate = new DateTime($exam['exam_date']);
                        $now = new DateTime();
                        $status = 'upcoming';
                        $statusClass = 'bg-primary';
                        
                        if ($examDate->format('Y-m-d') == $now->format('Y-m-d')) {
                            $status = 'today';
                            $statusClass = 'bg-warning';
                        } elseif ($examDate < $now) {
                            $status = 'completed';
                            $statusClass = 'bg-success';
                        }
                        ?>
                        <tr>
                            <td>
                                <div class="fw-medium"><?php echo htmlspecialchars($exam['title']); ?></div>
                                <div class="small text-muted"><?php echo htmlspecialchars($exam['description'] ?? ''); ?></div>
                            </td>
                            <td>
                                <div class="fw-medium"><?php echo $examDate->format('M j, Y'); ?></div>
                                <div class="small text-muted"><?php echo $examDate->format('g:i A'); ?></div>
                            </td>
                            <td><?php echo $exam['duration']; ?> minutes</td>
                            <td class="fw-medium"><?php echo $exam['total_marks']; ?> marks</td>
                            <td>
                                <span class="badge <?php echo $statusClass; ?>"><?php echo ucfirst($status); ?></span>
                            </td>
                            <td>
                                <div class="d-flex gap-1">
                                    <button class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-secondary">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>

<!-- Add Exam Modal -->
<div class="modal fade" id="addExamModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Exam</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="title" class="form-label">Exam Title</label>
                        <input type="text" class="form-control" id="title" name="title" required>
                    </div>
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="exam_date" class="form-label">Exam Date & Time</label>
                        <input type="datetime-local" class="form-control" id="exam_date" name="exam_date" required>
                    </div>
                    <div class="mb-3">
                        <label for="duration" class="form-label">Duration (minutes)</label>
                        <input type="number" class="form-control" id="duration" name="duration" required>
                    </div>
                    <div class="mb-3">
                        <label for="total_marks" class="form-label">Total Marks</label>
                        <input type="number" class="form-control" id="total_marks" name="total_marks" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-clear" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="add_exam" class="btn btn-add">Add Exam</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/../includes/footer.php'; ?>
